
// Additional Notice & Orders and Notice & Orders
export const INTERVALS = ['today','last1week', 'last15days', 'last1month'];

export const FOLDER_TYPES = [
  'Notices','Orders','Intimations','Reports','DRC-07A','Upload Offline APL-04 Orders','Proceedings',
];

export const RESPONSE_TYPES = ['REPLIED','NOT_REPLIED','NA'];

export const CASE_TYPES = ['OPEN','CLOSED'];

export const CASETYPE_NAMES = [
 'DETERMINATION OF TAX',
  'SCRUTINY OF RETURNS',
  'VOLUNTARY PAYMENT',
  'AUDIT',
  'DETERMINATION OF TAX MFY',
  'LETTER OF UNDERTAKING',
  'RECTIFICATION OF ORDERS',
  'APPEAL',
  'PRE-GST RECOVERY',
  'NON-FILERS OF RETURNS',
  'PENALTY',
  'ENFORCEMENT CASE',
  'TAX COLLECTED NOT DEPOSITED',
  'INTIMATION FOR RECOVERY U/S 79',
  'DEFERRED/PAYMENT INSTALMENTS',
  'RECOVERY',
  'APPEAL TO HIGHER AUTHORITY',
  'REMANDED AND APPEAL EFFECTS',
  'PENALTY MFY',
  'WAIVER SCHEME U/S 128A',
  'RECTIFICATION OF ORDERS MFY',
  'DOT AND PENALTY MFY',
  'APPEAL BY TAX DEPARTMENT',
  'UNREGISTERED PERSONS MFY',
  'ED CASE',
  'REFUNDS',
  'Waiver Scheme u/s 128A',
  'Non Filing Returns',
  null
];

export const uniqueType = [
 'distinct'
];

export const removeReminderType = [
 'remove','show'
];

// Ledgers
export const TYPES = ['LIABILITY','CASH','ITC','ITC_REVERSAL','RCM','NEGATIVE_LIABILITY'];

//REPORTS
export const STATUS = ['PENDING','COMPLETED','INQUEUE'];
export const REMARKS = [
  'Success',
  'Invalid Username or Password. Please try again.',
  'You have entered a wrong password for 3 consecutive times. Your account has been locked. Kindly access ‘Forgot Password’ link to reset your password.',
  'Your GST Login Password is Expired. Please change it with New Password!',
  'Problem in login into gstr website try after some time.',
];

export const clientNameRegex = /^[^<>:"\/\\|?*\.]*$/;

export const gstInRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][1-9A-Z]Z[0-9A-Z]$/;

// GST RETURNS
export const filingType = ["Not Filed", "Filed"];

export const regularRegistrationDashboardMenu = [ "GSTR1", "GSTR1A", "GSTR2X", "GSTR3B", "CMP08", "GSTR4X", "GSTR9", "GSTR9C", "GSTR8", "GSTR7"];

export const months = ['April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December', 'January', 'February', 'March'];

export const RegularTaxHeadings = ['GSTR1', 'GSTR1A', 'GSTR2X', 'GSTR3B', 'CMP08', 'GSTR4', 'GSTR9', 'GSTR9C', 'GSTR7', 'GSTR8', 'GST_NOT_VERIFIED' ];

export const gstReturnsCategories = ["regular_taxpayer", "tax_collector", "tax_deductor"];

export const quarters = ['Q1','Q2','Q3','Q4'];

export const monthsNum = ['04','05','06','07','08','09','10','11','12','01','02','03'];

export const placeOfSupplyStateCodes = ["01","02","03","04","05","06","07","08","09","10","11","12","13","14","15","16","17","18","19","20","21","22","23","24","25","26","27","29","30","31","32","33","34","35","36","37","38","96","97"];


