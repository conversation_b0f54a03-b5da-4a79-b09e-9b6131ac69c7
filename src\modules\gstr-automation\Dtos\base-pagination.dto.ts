import { Transform } from 'class-transformer';
import { IsInt, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';

export class BasePaginationDto {
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : Number(value))
  @IsInt()
  @Min(0)
  offset: number = 0;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : Number(value))
  @IsInt()
  @Min(1)
  @Max(100)
  limit: number = 10;
}
