import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';
import { TAN_REGEX } from 'src/utils/validations/regex-pattrens';
import { clientNameRegex } from './constants.dto';

export function IsNameOrTan(validationOptions?: ValidationOptions) {
  const nameRegex = clientNameRegex;
  const tanRegex = TAN_REGEX;
  return (target: Object, propertyName: string) => {
    registerDecorator({
      name: 'IsNameOrTAN',
      target: target.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {
          if (value == null || value === '') return true;
          if (typeof value !== 'string') return false;
          const v = value.trim();
          const maybe = v.replace(/\s+/g, '').toUpperCase();
          if (maybe.length === 15 && /^[0-9A-Z]+$/.test(maybe)) return tanRegex.test(maybe);
          return nameRegex.test(v);
        },
        defaultMessage(args?: ValidationArguments) {
          return `${args?.property} must be a valid client name or a 10-char TAN`;
        },
      },
    });
  };
}
