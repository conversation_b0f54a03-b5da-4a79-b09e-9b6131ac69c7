import { IsOptional, IsObject, IsString, IsIn, ValidateNested } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class SortDto {
    @IsString()
    @IsIn(['displayName'])
    column: string;

    @IsString()
    @IsIn(['asc', 'desc'])
    direction: 'asc' | 'desc';
}

export class IncomeTaxNewUpdatesQueryDto {
    @IsOptional()
    @Transform(({ value }) => {
        try {
            return typeof value === 'string' ? JSON.parse(value) : value;
        } catch {
            return {};
        }
    })
    @ValidateNested()
    @Type(() => SortDto)
    sort?: SortDto;
}
