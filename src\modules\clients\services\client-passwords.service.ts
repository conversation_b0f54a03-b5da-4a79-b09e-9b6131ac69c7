import { BadRequestException, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Event_Actions } from 'src/event-listeners/actions';
import Client from 'src/modules/clients/entity/client.entity';
import { CreateClientPasswordDto, UpdateClientPasswordDto } from '../dto/create-password.dto';
import Password, { IsExistingAtomPro } from '../entity/password.entity';
import * as xlsx from 'xlsx';
import * as ExcelJS from 'exceljs';
import ClientGroup from 'src/modules/client-group/client-group.entity';
import { createQueryBuilder, Not, getConnection, getManager } from 'typeorm';
import Activity, { ActivityType } from 'src/modules/activity/activity.entity';
import { User } from 'src/modules/users/entities/user.entity';
import AutClientCredentials, {
  IncomeTaxStatus,
  syncStatus,
} from 'src/modules/automation/entities/aut_client_credentials.entity';
import GstrCredentials, {
  GstrStatus,
} from 'src/modules/gstr-automation/entity/gstrCredentials.entity';
import OrganizationPreferences from 'src/modules/organization-preferences/entity/organization-preferences.entity';
import checkGstrUsername from 'src/utils/validations/atom-pro/gstrUserName';
import checkGstrPassword from 'src/utils/validations/atom-pro/gstrPassword';
import checkPanNumber, { checkTanNumber } from 'src/utils/validations/atom-pro/panNumber';
import { getRepository } from 'typeorm';
import TanClientCredentials from 'src/modules/tan-automation/entity/tan-client-credentials.entity';
import { formatDate } from 'src/utils';
import checkTracesUserId from 'src/utils/validations/atom-pro/tracesUserId';
import checkTracesPassword from 'src/utils/validations/atom-pro/tracesPassword';

@Injectable()
export class ClientPasswordService {
  constructor(private eventEmitter: EventEmitter2) {}

  async create(userId: number, data: CreateClientPasswordDto) {
    let user = await User.findOne({ where: { id: userId } });
    const client = await Client.findOne({ where: { id: data.client } });
    const clientGroup = await ClientGroup.findOne({ where: { id: data.clientGroup } });
    const clientPasswordData = await Password.find({ where: { client: client } });

    if (clientPasswordData?.length > 0) {
      const existingPasswordRecord = clientPasswordData.filter((password) => {
        return password?.website === data?.website;
      });
      if (existingPasswordRecord?.length > 0) {
        throw new BadRequestException(`Credentials already exists with website: ${data?.website}`);
      }
    }

    const addPassword = async (passwordData: any, isaddAtomPro: any) => {
      const isLoginIdExists = await Password.findOne({
        where: {
          loginId: passwordData.loginId.trim(),
          website:passwordData.website,
          client: {
            organization: {
              id: user?.organization?.id,
            },
          },
        },
        relations: ['client', 'client.organization'],
      });

      if (isLoginIdExists) {
        throw new BadRequestException(`Login Id already exists!`);
      }
      const password = new Password();
      password.website = passwordData.website.trim();
      password.websiteUrl = passwordData.websiteUrl.trim();
      password.loginId = passwordData.loginId.trim();
      password.password = passwordData.password.trim();
      password.client = client;
      password.clientGroup = clientGroup;
      password['userId'] = userId;
      password.isExistingAtomPro = isaddAtomPro;
      password.tracesTan = passwordData?.traceTan;
      await password.save();

      let activity = new Activity();
      activity.action = Event_Actions.CREDENTIALS_ADDED;
      activity.actorId = user.id;
      activity.type = data.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
      activity.typeId = password?.client ? password?.client?.id : password?.clientGroup?.id;
      activity.remarks = `"${password?.website}" Credentials Added by ${user?.fullName}`;
      await activity.save();

      this.eventEmitter.emit(Event_Actions.CREDENTIAL_CREATED, {
        userId,
        clientId: client?.id,
      });

      return password;
    };

    const executeTanAndTraceTransaction = async (tanDetails: any, traceDetails: any) => {
      let queryRunner = getConnection().createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        const isLoginIdExists = await Password.findOne({
          where: {
            loginId: tanDetails.tanLoginId.trim(),
            client: {
              organization: {
                id: user?.organization?.id,
              },
            },
          },
          relations: ['client', 'client.organization'],
        });

        if (isLoginIdExists) {
          throw new BadRequestException('Login Id already exists');
        }
        // Add TAN password
        const tanPassword = new Password();
        tanPassword.website = 'Income Tax | e-Filing (TAN)';
        tanPassword.websiteUrl = 'https://eportal.incometax.gov.in/iec/foservices/#/login';
        tanPassword.loginId = tanDetails.tanLoginId;
        tanPassword.password = tanDetails.tanPassword || null;
        tanPassword.client = client;
        tanPassword.clientGroup = clientGroup;
        tanPassword['userId'] = userId;
        tanPassword.isExistingAtomPro = tanDetails.isaddAtomPro;
        await queryRunner.manager.save(tanPassword);

        const isTracesLoginIdExists = await Password.findOne({
          where: {
            tracesTan: traceDetails.traceTan.trim(),
            client: {
              organization: {
                id: user?.organization?.id,
              },
            },
          },
          relations: ['client', 'client.organization'],
        });

        if (isTracesLoginIdExists) {
          throw new BadRequestException('TAN already exists');
        }

        // Add Trace password
        const tracePassword = new Password();
        tracePassword.website = 'Income Tax | Traces (Tax Deductor)';
        tracePassword.websiteUrl = 'https://www.tdscpc.gov.in/app/login.xhtml?usr=Ded';
        tracePassword.loginId = traceDetails.traceUserId || null;
        tracePassword.password = traceDetails.tracePassword || null;
        tracePassword.tracesTan = traceDetails.traceTan;
        tracePassword.client = client;
        tracePassword.clientGroup = clientGroup;
        tracePassword['userId'] = userId;
        tracePassword.isExistingAtomPro = traceDetails.isaddAtomPro;
        await queryRunner.manager.save(tracePassword);

        // Log activity for TAN
        const tanActivity = new Activity();
        tanActivity.action = Event_Actions.CREDENTIALS_ADDED;
        tanActivity.actorId = user.id;
        tanActivity.type = data.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
        tanActivity.typeId = tanPassword.client
          ? tanPassword.client.id
          : tanPassword.clientGroup.id;
        tanActivity.remarks = `"${tanPassword.website}" Credentials Added by ${user?.fullName}`;
        await queryRunner.manager.save(tanActivity);

        // Log activity for Trace
        const traceActivity = new Activity();
        traceActivity.action = Event_Actions.CREDENTIALS_ADDED;
        traceActivity.actorId = user.id;
        traceActivity.type = data.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
        traceActivity.typeId = tracePassword.client
          ? tracePassword.client.id
          : tracePassword.clientGroup.id;
        traceActivity.remarks = `"${tracePassword.website}" Credentials Added by ${user?.fullName}`;
        await queryRunner.manager.save(traceActivity);

        // Emit events
        this.eventEmitter.emit(Event_Actions.CREDENTIAL_CREATED, {
          userId,
          clientId: client?.id,
        });

        await queryRunner.commitTransaction();
        return { tanPassword, tracePassword };
      } catch (error) {
        await queryRunner.rollbackTransaction();
        throw error;
      } finally {
        await queryRunner.release();
      }
    };

    let isaddAtomPro = null;
    if (data?.atomProAdd) {
      if (data?.websiteUrl === 'https://services.gst.gov.in/services/login') {
        const gstrUsername = data.loginId;
        const gstrPassword = data.password;
        const checkGstrUserNameValid = checkGstrUsername(gstrUsername);
        const checkGstrPasswordValid = checkGstrPassword(gstrPassword);
        if (checkGstrUserNameValid) {
          throw new BadRequestException('GSTR User Name Is Invalid');
        } else {
          if (checkGstrPasswordValid) {
            throw new BadRequestException('GSTR Password Is Invalid');
          } else {
            const existingClinet = await GstrCredentials.findOne({
              where: {
                client: client,
              },
            });
            if (existingClinet) {
              throw new BadRequestException('Client already exist in Gstr');
            } else {
              const checkCredential = await GstrCredentials.findOne({
                where: { organizationId: user.organization.id, userName: data.loginId },
              });
              if (checkCredential) {
                throw new BadRequestException('Already User Id Existing in this Organization');
              } else {
                const gstrCredentialsCount = await GstrCredentials.count({
                  where: { organizationId: user.organization.id, status: GstrStatus.ENABLE },
                });
                const organizationPreferences: any = await OrganizationPreferences.findOne({
                  where: { organization: user?.organization },
                });

                const gstrAccess = organizationPreferences?.automationConfig?.gstr === 'YES';
                const organizationGstrLimit = organizationPreferences?.automationConfig?.gstrLimit;
                if (gstrAccess) {
                  if (organizationGstrLimit > gstrCredentialsCount) {
                    isaddAtomPro = IsExistingAtomPro.YES;
                    const passwordData = await addPassword(data, isaddAtomPro);
                    const gstrCredentials = new GstrCredentials();
                    gstrCredentials.userName = String(data.loginId);
                    gstrCredentials.password = String(data.password);
                    gstrCredentials.userId = user?.id;
                    gstrCredentials.client = client;
                    gstrCredentials.organizationId = user?.organization?.id;
                    gstrCredentials.passwordId = passwordData?.id;
                    gstrCredentials.status = GstrStatus.ENABLE;
                    await gstrCredentials.save();

                    return 'Atom Pro GSTR Client added Successfully';
                  } else {
                    throw new BadRequestException(
                      'Atom Pro Organization Gstr Client Count Reached',
                    );
                  }
                } else {
                  throw new BadRequestException('Atom Pro Organization No Access');
                }
              }
            }
          }
        }
      } else if (
        data.websiteUrl === 'https://eportal.incometax.gov.in/iec/foservices/#/login' &&
        data?.website === 'Income Tax | e-Filing (PAN)'
      ) {
        const incomeTaxPan = data.loginId;
        const checkPanValid = checkPanNumber(incomeTaxPan);
        if (checkPanValid) {
          throw new BadRequestException('Income Tax Pan Invalid');
        } else {
          const autClientCredential = await AutClientCredentials.count({
            where: { organizationId: user?.organization?.id, status: IncomeTaxStatus.ENABLE },
          });
          const existingClinet = await AutClientCredentials.findOne({
            where: {
              client: client,
            },
          });
          if (existingClinet) {
            throw new BadRequestException('Client already exist in Atom Pro Income Tax');
          } else {
            const existingRecord = await AutClientCredentials.findOne({
              where: { organizationId: user?.organization?.id, panNumber: data.loginId },
            });
            if (existingRecord) {
              throw new BadRequestException(
                'This PAN are already Present in Income Tax in this Organization',
              );
            } else {
              const organizationPreferences: any = await OrganizationPreferences.findOne({
                where: { organization: user?.organization },
              });
              const incomeTaxAccess =
                organizationPreferences?.automationConfig?.incomeTax === 'YES';
              const organizationIncomeTaxLimit =
                organizationPreferences?.automationConfig?.incomeTaxLimit;
              if (incomeTaxAccess) {
                if (organizationIncomeTaxLimit > autClientCredential) {
                  isaddAtomPro = IsExistingAtomPro.YES;
                  const passwordData = await addPassword(data, isaddAtomPro);
                  const clientCredentials = new AutClientCredentials();
                  clientCredentials.panNumber = String(data.loginId).trim();
                  clientCredentials.password = String(data?.password).trim();
                  clientCredentials.client = client;
                  clientCredentials.organizationId = user?.organization?.id;
                  clientCredentials.syncStatus = syncStatus.NOTSYNC;
                  clientCredentials.passwordId = passwordData?.id;
                  clientCredentials.status = IncomeTaxStatus.ENABLE;
                  await clientCredentials.save();

                  return 'Atom Pro Income Tax Client added Successfully';
                } else {
                  throw new BadRequestException(
                    'Atom Pro Organization Income Tax Client Count Reached',
                  );
                }
              } else {
                throw new BadRequestException('Atom Pro Organization Income Tax No Access');
              }
            }
          }
        }
      } else if (
        data.websiteUrl === 'https://eportal.incometax.gov.in/iec/foservices/#/login' &&
        data?.website === 'Income Tax | e-Filing (TAN)'
      ) {
        const incomeTaxTan = data.loginId;
        const checkTanValid = checkTanNumber(incomeTaxTan);
        if (checkTanValid) {
          throw new BadRequestException('Income Tax TAN Invalid');
        } else {
          const tanClientCredential = await TanClientCredentials.count({
            where: { organizationId: user?.organization?.id, status: IncomeTaxStatus.ENABLE },
          });
          const existingClinet = await TanClientCredentials.findOne({
            where: {
              client: client,
            },
          });
          if (existingClinet) {
            throw new BadRequestException('Client already exist in Atom Pro Income Tax');
          } else {
            const existingRecord = await TanClientCredentials.findOne({
              where: { organizationId: user?.organization?.id, tanNumber: data.loginId },
            });
            if (existingRecord) {
              throw new BadRequestException(
                'This TAN are already Present in Income Tax in this Organization',
              );
            } else {
              const tanDetails = {
                tanLoginId: data.loginId.trim(),
                tanPassword: data.password.trim(),
                isaddAtomPro: IsExistingAtomPro.YES,
              };
              const tracesDetails = {
                traceTan: data.loginId.trim(),
                tracePassword: null,
                traceUserId: null,
                isaddAtomPro: IsExistingAtomPro.YES,
              };
              const organizationPreferences: any = await OrganizationPreferences.findOne({
                where: { organization: user?.organization },
              });
              const incomeTaxTanAccess = organizationPreferences?.automationConfig?.tan === 'YES';
              const organizationIncomeTaxTanLimit =
                organizationPreferences?.automationConfig?.tanLimit;
              if (incomeTaxTanAccess) {
                if (organizationIncomeTaxTanLimit > tanClientCredential) {
                  //add tan client credentials with traces as null values and insert client credentilas
                  isaddAtomPro = IsExistingAtomPro.YES;
                  // const passwordData = await addTanPassword(tanDetails);
                  // const passwordTraceData = await addTracePassword(tracesDetails);
                  let { tanPassword, tracePassword } = await executeTanAndTraceTransaction(
                    tanDetails,
                    tracesDetails,
                  );

                  const clientCredentials = new TanClientCredentials();
                  clientCredentials.tanNumber = String(data.loginId).trim();
                  clientCredentials.password = String(data?.password).trim();
                  clientCredentials.client = client;
                  clientCredentials.organizationId = user?.organization?.id;
                  clientCredentials.passwordId = tanPassword?.id;
                  clientCredentials.status = IncomeTaxStatus.ENABLE;
                  (clientCredentials.traceUserId = null), (clientCredentials.tracePassword = null);
                  await clientCredentials.save();

                  return 'Atom Pro Income Tax TAN Client added Successfully';
                } else {
                  throw new BadRequestException(
                    'Atom Pro Organization Income Tax TAN Client Count Reached',
                  );
                }
              } else {
                throw new BadRequestException('Atom Pro Organization Income Tax TAN No Access');
              }
            }
          }
        }
      } else if (data.websiteUrl === 'https://www.tdscpc.gov.in/app/login.xhtml?usr=Ded') {
        const incomeTaxTan = data.tracesTan;
        const checkTanValid = checkTanNumber(incomeTaxTan);
        const checkValidTracesUserId = checkTracesUserId(data.loginId.trim());
        const checkValidTracesPassword = checkTracesPassword(data.password.trim());
        if (checkValidTracesUserId) {
          throw new BadRequestException('Trace User Id is Invalid');
        }
        if (checkValidTracesPassword) {
          throw new BadRequestException('Trace Password is Invalid');
        }
        if (checkTanValid) {
          throw new BadRequestException('Income Tax TAN Invalid');
        } else {
          const tanClientCredential = await TanClientCredentials.count({
            where: { organizationId: user?.organization?.id, status: IncomeTaxStatus.ENABLE },
          });
          const existingClinet = await TanClientCredentials.findOne({
            where: {
              tanNumber: data?.tracesTan,
            },
          });
          if (existingClinet) {
            throw new BadRequestException(
              `Client already exist in Atom Pro Traces with this ${data?.tracesTan}`,
            );
          } else {
            const existingRecord = await TanClientCredentials.findOne({
              where: { organizationId: user?.organization?.id, traceUserId: data?.loginId },
            });
            if (existingRecord) {
              throw new BadRequestException(
                'This Trace login is already Present in Traces in this Organization',
              );
            } else {
              const tanDetails = {
                tanLoginId: data.tracesTan.trim(),
                tanPassword: null,
                isaddAtomPro: IsExistingAtomPro.YES,
              };
              const tracesDetails = {
                traceTan: data.tracesTan.trim(),
                tracePassword: data.password.trim(),
                traceUserId: data.loginId.trim(),
                isaddAtomPro: IsExistingAtomPro.YES,
              };
              const organizationPreferences: any = await OrganizationPreferences.findOne({
                where: { organization: user?.organization },
              });
              const incomeTaxTanAccess = organizationPreferences?.automationConfig?.tan === 'YES';
              const organizationIncomeTaxTanLimit =
                organizationPreferences?.automationConfig?.tanLimit;
              if (incomeTaxTanAccess) {
                if (organizationIncomeTaxTanLimit > tanClientCredential) {
                  //add tan client credentials with traces as null values and insert client credentilas
                  isaddAtomPro = IsExistingAtomPro.YES;
                  // const passwordData = await addTanPassword(tanDetails);
                  // const passwordTraceData = await addTracePassword(tracesDetails);
                  let { tanPassword, tracePassword } = await executeTanAndTraceTransaction(
                    tanDetails,
                    tracesDetails,
                  );
                  const clientCredentials = new TanClientCredentials();
                  clientCredentials.tanNumber = String(data.tracesTan).trim();
                  clientCredentials.password =  null;
                  clientCredentials.client = client;
                  clientCredentials.organizationId = user?.organization?.id;
                  clientCredentials.passwordId = tanPassword?.id;
                  clientCredentials.status = IncomeTaxStatus.ENABLE;
                  clientCredentials.traceUserId = String(data.loginId).trim();
                  clientCredentials.tracePassword = String(data.password).trim();
                  await clientCredentials.save();

                  return 'Atom Pro Income Tax TAN Client added Successfully';
                } else {
                  throw new BadRequestException(
                    'Atom Pro Organization Income Tax TAN Client Count Reached',
                  );
                }
              } else {
                throw new BadRequestException('Atom Pro Organization Income Tax TAN No Access');
              }
            }
          }
        }
      } else {
        await addPassword(data, isaddAtomPro);
      }
    } else {
      if (data?.website === 'Income Tax | e-Filing (TAN)') {
        const tanDetails = {
          tanLoginId: data.loginId.trim(),
          tanPassword: data.password.trim(),
          isaddAtomPro: IsExistingAtomPro.NO,
        };
        const tracesDetails = {
          traceTan: data.loginId.trim(),
          tracePassword: null,
          traceUserId: null,
          isaddAtomPro: IsExistingAtomPro.NO,
        };

        // await addTanPassword(tanDetails);
        // await addTracePassword(tracesDetails);
        await executeTanAndTraceTransaction(tanDetails, tracesDetails);
      } else if (data?.website === 'Income Tax | Traces (Tax Deductor)') {
        const tanDetails = {
          tanLoginId: data.tracesTan.trim(),
          tanPassword: null,
          isaddAtomPro: IsExistingAtomPro.NO,
        };
        const tracesDetails = {
          traceTan: data.tracesTan.trim(),
          tracePassword: data.password.trim(),
          traceUserId: data.loginId.trim(),
          isaddAtomPro: IsExistingAtomPro.NO,
        };

        // await addTanPassword(tanDetails);
        // await addTracePassword(tracesDetails);
        await executeTanAndTraceTransaction(tanDetails, tracesDetails);
      } else {
        await addPassword(data, isaddAtomPro);
      }
    }
  }

  async find(query: any) {
    let client = await Client.findOne({
      where: {
        id: query?.clientId,
      },
      relations: ['organization'],
    });

    let clientGroupData = await ClientGroup.findOne({
      where: {
        id: query?.clientGroupId,
      },
      relations: ['organization'],
    });

    let passwords = await createQueryBuilder(Password, 'password')
      .leftJoinAndSelect('password.client', 'client')
      .leftJoinAndSelect('password.autClientCredentials', 'autClientCredentials')
      .leftJoinAndSelect('password.gstrCredentials', 'gstrCredentials')
      .leftJoinAndSelect('password.tanClientCredentials', 'tanClientCredentials')
      .leftJoinAndSelect('password.clientGroup', 'clientGroup');

    if (query?.clientId) {
      passwords.where('client.id = :id', { id: query?.clientId });
    }

    if (query?.clientGroupId) {
      passwords.orWhere('clientGroup.id = :id', { id: query?.clientGroupId });

      const clientGroup = await createQueryBuilder(ClientGroup, 'clientGroup')
        .leftJoinAndSelect('clientGroup.organization', 'organization')
        .leftJoinAndSelect('clientGroup.clients', 'clients')
        .where('clientGroup.id = :id', { id: query?.clientGroupId })
        .andWhere('organization.id = :organization', {
          organization: client?.organization?.id
            ? client?.organization?.id
            : clientGroupData?.organization?.id,
        })
        .getOne();

      const clientGroupIDs = clientGroup?.clients?.map((item) => item.id).length
        ? clientGroup?.clients?.map((item) => item.id)
        : [];
      if (clientGroupIDs?.length) {
        passwords.orWhere('client.id IN (:...clientGroupIDs)', { clientGroupIDs: clientGroupIDs });
      }
    }
    const passwordData = await passwords.getMany();

    const tanCredentials = passwordData.find(
      (item) => item.website === 'Income Tax | e-Filing (TAN)',
    );

    let data = [];

    if (tanCredentials) {
      data = passwordData.map((password) => {
        if (
          password.website === 'Income Tax | Traces (Tax Deductor)' &&
          password.tracesTan === tanCredentials?.loginId
        ) {
          return {
            ...password,
            status: tanCredentials?.tanClientCredentials?.status,
            tanClientCredentialsId: tanCredentials?.tanClientCredentials?.id,
          };
        } else {
          return {
            ...password,
          };
        }
      });
    } else {
      data = passwordData;
    }
    return data;
  }
  async exportClientPasswordexport(clientId, query, userId) {
    const id = clientId;
    let clientpasswords;

    try {
      clientpasswords = await this.find(query);
    } catch (error) {
      throw new BadRequestException('Error fetching data for export');
    }

    if (!clientpasswords || !clientpasswords.length) {
      throw new BadRequestException('No Data for Export');
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Credentials');

    // Determine if the data belongs to a client group
    const isClientGroup = clientpasswords.some(
      (item) => item?.clientGroup?.type === 'CLIENT_GROUP',
    );

    const headers = isClientGroup
      ? [
          { header: 'S.No', key: 'serialNo' },
          { header: 'Client / Client Group', key: 'client' },
          { header: 'Website', key: 'website' },
          { header: 'Website Url', key: 'websiteurl' },
          { header: 'Login ID', key: 'loginId' },
          { header: 'Password', key: 'password' },
          { header: 'Last Modified On', key: 'lastmodifiedOn' },
        ]
      : [
          { header: 'S.No', key: 'serialNo' },
          { header: 'Website', key: 'website' },
          { header: 'Website Url', key: 'websiteurl' },
          { header: 'Login ID', key: 'loginId' },
          { header: 'Password', key: 'password' },
          { header: 'Last Modified On', key: 'lastmodifiedOn' },
        ];

    worksheet.columns = headers;
    let serialCounter = 1; // Initialize a counter
    clientpasswords.forEach((clientpassword) => {
      const rowData = isClientGroup
        ? {
            serialNo: serialCounter++, // Assign and then increment the counter
            client: clientpassword?.client?.displayName || clientpassword?.clientGroup?.displayName,
            website: clientpassword?.website,
            websiteurl: {
              text: clientpassword?.websiteUrl,
              hyperlink: clientpassword?.websiteUrl,
            },
            loginId: clientpassword.loginId,
            password: clientpassword?.password,
            lastmodifiedOn: formatDate(clientpassword?.updatedAt),
          }
        : {
            serialNo: serialCounter++, // Assign and then increment the counter
            website: clientpassword?.website,
            websiteurl: {
              text: clientpassword?.websiteUrl,
              hyperlink: clientpassword?.websiteUrl,
            },
            loginId: clientpassword.loginId,
            password: clientpassword?.password,
            lastmodifiedOn: formatDate(clientpassword?.updatedAt),
          };

      const excelRow = worksheet.addRow(rowData);

      // Apply blue color and underline to the 'websiteurl' column
      const websiteUrlCell = excelRow.getCell('websiteurl');
      if (websiteUrlCell.value) {
        websiteUrlCell.font = { color: { argb: '0000FF' }, underline: true };
      }
    });

    worksheet.columns.forEach((column) => {
      let maxLength = 0;
      column.eachCell({ includeEmpty: true }, (cell) => {
        const cellLength = cell.value ? cell.value.toString().length : 0;
        maxLength = Math.max(maxLength, cellLength);
      });
      column.width = maxLength + 2;
    });

    const headerRow = worksheet.getRow(1);
    headerRow.eachCell((cell) => {
      cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: '64B5F6' } };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.font = { bold: true };
    });

    worksheet.columns.forEach((column) => {
      if (column.key === 'websiteurl') {
        column.width = 50;
        column.alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' }; // Enable text wrap
      } else {
        column.alignment = { horizontal: 'center', vertical: 'middle' }; // Apply center alignment for other columns
      }
    });

    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber !== 1) {
        row.eachCell((cell) => {
          cell.alignment = { vertical: 'middle', horizontal: 'center' };
        });
      }
    });

    worksheet.views = [{ state: 'frozen', ySplit: 1 }];

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  }

  async update(userId: number, id: number, body: UpdateClientPasswordDto) {
    let user = await User.findOne({ where: { id: userId } });
    let password = await Password.findOne({ where: { id }, relations: ['client', 'clientGroup'] });
    const client = await Client.findOne({ where: { id: password?.client?.id } });

    const updatePassword = async (body: any, isaddAtomPro: any) => {
      const isLoginIdExists = await Password.findOne({
        where: {
          loginId: body.loginId.trim(),
          website:body.website,
          id: Not(id),
          client: {
            organization: {
              id: user?.organization?.id,
            },
          },
        },
        relations: ['client', 'client.organization'],
      });

      if (isLoginIdExists) {
        throw new BadRequestException(`Login Id already exists for client: ${client?.displayName}`);
      }
      password.website = body.website.trim();
      password.websiteUrl = body.websiteUrl.trim();
      password.loginId = body.loginId.trim();
      password.password = body.password.trim();
      password.isExistingAtomPro = isaddAtomPro;
      password['userId'] = userId;
      password.tracesTan = body?.tracesTan;
      await password.save();

      let activity = new Activity();
      activity.action = Event_Actions.CREDENTIALS_UPDATED;
      activity.actorId = user.id;
      activity.type = password.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
      activity.typeId = password.client ? password.client.id : password.clientGroup.id;
      activity.remarks = `"${password.website}" Credentials Updated by ${user.fullName}`;
      await activity.save();

      this.eventEmitter.emit(Event_Actions.CREDENTIAL_UPDATED, {
        userId,
        clientId: password?.client?.id,
      });
      return password;
    };

    const updateTracePassword = async (body: any, isaddAtomPro: any) => {
      const isLoginIdExists = await Password.findOne({
        where: {
          tracesTan: body.tracesTan.trim(),
          id: Not(id),
          client: {
            organization: {
              id: user?.organization?.id,
            },
          },
        },
        relations: ['client', 'client.organization'],
      });

      if (isLoginIdExists) {
        throw new BadRequestException('Login Id already exists!');
      }
      password.website = body.website.trim();
      password.websiteUrl = body.websiteUrl.trim();
      password.loginId = body.loginId.trim();
      password.password = body.password.trim();
      password.isExistingAtomPro = isaddAtomPro;
      password['userId'] = userId;
      password.tracesTan = body.tracesTan.trim();
      await password.save();

      let activity = new Activity();
      activity.action = Event_Actions.CREDENTIALS_UPDATED;
      activity.actorId = user.id;
      activity.type = password.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
      activity.typeId = password.client ? password.client.id : password.clientGroup.id;
      activity.remarks = `"${password.website}" Credentials Updated by ${user.fullName}`;
      await activity.save();

      this.eventEmitter.emit(Event_Actions.CREDENTIAL_UPDATED, {
        userId,
        clientId: password?.client?.id,
      });
      return password;
    };

    let updateTanAndTracesCredentials = async (tanDetails: any, traceDetails: any) => {
      const client = await Client.findOne({ where: { id: tanDetails?.client?.id } });
      let tanPassword: Password = await Password.findOne({ where: { id: id } });
      let tracePassword: Password = await Password.findOne({
        where: {
          client: client,
          website: 'Income Tax | Traces (Tax Deductor)',
          tracesTan: tanDetails.tanLoginId,
        },
      });
      // let tanPassword: Password, tracePassword: Password;

      await getManager().transaction(async (transactionalEntityManager) => {
        // Create TAN credentials within the transaction
        tanPassword.loginId = tanDetails.tanLoginId;
        tanPassword.password = tanDetails.tanPassword;
        tanPassword['userId'] = userId;
        tanPassword.isExistingAtomPro = tanDetails.isaddAtomPro;
        await transactionalEntityManager.save(tanPassword);

        // Create Traces credentials within the transaction
        tracePassword.loginId = traceDetails.traceUserId;
        tracePassword.password = traceDetails.tracePassword;
        tracePassword.tracesTan = tanDetails?.traceTan;
        tracePassword['userId'] = userId;
        tracePassword.isExistingAtomPro = traceDetails.isaddAtomPro;
        await transactionalEntityManager.save(tracePassword);
      });

      return { tanPassword, tracePassword };
    };

    const executeTanAndTraceTransaction = async (tanDetails: any, traceDetails: any) => {
      let queryRunner = getConnection().createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        let tanPassword: Password, tracePassword: Password;

        if (password?.website === 'Income Tax | e-Filing (TAN)') {
          const isLoginIdExists = await Password.findOne({
            where: {
              loginId: tanDetails.tanLoginId.trim(),
              id: Not(id),
              client: {
                organization: {
                  id: user?.organization?.id,
                },
              },
            },
            relations: ['client', 'client.organization'],
          });

          if (isLoginIdExists) {
            throw new BadRequestException('Cannot Update! Login Id already exists');
          }
          // Add TAN password
          tanPassword = password;
          tanPassword.website = 'Income Tax | e-Filing (TAN)';
          tanPassword.websiteUrl = 'https://eportal.incometax.gov.in/iec/foservices/#/login';
          tanPassword.loginId = tanDetails.tanLoginId;
          tanPassword.password = tanDetails.tanPassword || null;
          tanPassword.client = client;
          tanPassword['userId'] = userId;
          tanPassword.isExistingAtomPro = tanDetails.isaddAtomPro;
          await queryRunner.manager.save(tanPassword);

          let tracesTanRecord = await Password.findOne({
            where: {
              tracesTan: traceDetails.traceTan.trim(),
              client: client,
            },
          });

          if (!tracesTanRecord) {
            throw new BadRequestException('Cannot Update! No Traces Record Found with this TAN');
          }

          // Add Trace password
          tracePassword = tracesTanRecord;
          tracePassword.website = 'Income Tax | Traces (Tax Deductor)';
          tracePassword.websiteUrl = 'https://www.tdscpc.gov.in/app/login.xhtml?usr=Ded';
          tracePassword.loginId = traceDetails.traceUserId || null;
          tracePassword.password = traceDetails.tracePassword || null;
          tracePassword.tracesTan = traceDetails.traceTan;
          tracePassword.client = client;
          tracePassword['userId'] = userId;
          tracePassword.isExistingAtomPro = traceDetails.isaddAtomPro;
          await queryRunner.manager.save(tracePassword);
        } else if (password?.website === 'Income Tax | Traces (Tax Deductor)') {
          const isTracesLoginIdExists = await Password.findOne({
            where: {
              tracesTan: traceDetails.traceTan.trim(),
              id: Not(id),
              client: {
                organization: {
                  id: user?.organization?.id,
                },
              },
            },
            relations: ['client', 'client.organization'],
          });

          if (isTracesLoginIdExists) {
            throw new BadRequestException('Cannot Update! Trace TAN already exists');
          }

          // Add Trace password
          tracePassword = password;
          tracePassword.website = 'Income Tax | Traces (Tax Deductor)';
          tracePassword.websiteUrl = 'https://www.tdscpc.gov.in/app/login.xhtml?usr=Ded';
          tracePassword.loginId = traceDetails.traceUserId || null;
          tracePassword.password = traceDetails.tracePassword || null;
          tracePassword.tracesTan = traceDetails.traceTan;
          tracePassword.client = client;
          tracePassword['userId'] = userId;
          tracePassword.isExistingAtomPro = traceDetails.isaddAtomPro;
          await queryRunner.manager.save(tracePassword);

          const tanPasswordRec = await Password.findOne({
            where: {
              loginId: tanDetails.tanLoginId.trim(),
              client: client,
            },
          });

          if (!tanPasswordRec) {
            throw new BadRequestException('Cannot Update! Income Tax TAN not found for this Trace');
          }

          // if(isLoginIdExists){
          //   throw new BadRequestException('Cannot Update! Login Id already exists');
          // }
          // Add TAN password
          tanPassword = tanPasswordRec;
          tanPassword.website = 'Income Tax | e-Filing (TAN)';
          tanPassword.websiteUrl = 'https://eportal.incometax.gov.in/iec/foservices/#/login';
          tanPassword.loginId = tanDetails.tanLoginId;
          tanPassword.password = tanDetails.tanPassword || null;
          tanPassword.client = client;
          tanPassword['userId'] = userId;
          tanPassword.isExistingAtomPro = tanDetails.isaddAtomPro;
          await queryRunner.manager.save(tanPassword);
        }

        // Log activity for TAN
        const tanActivity = new Activity();
        tanActivity.action = Event_Actions.CREDENTIALS_UPDATED;
        tanActivity.actorId = user.id;
        tanActivity.type = tanPassword.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
        tanActivity.typeId = tanPassword.client
          ? tanPassword.client.id
          : tanPassword.clientGroup.id;
        tanActivity.remarks = `"${tanPassword.website}" Credentials Updated by ${user.fullName}`;
        await queryRunner.manager.save(tanActivity);

        // Log activity for Trace
        const traceActivity = new Activity();
        tanActivity.action = Event_Actions.CREDENTIALS_UPDATED;
        tanActivity.actorId = user.id;
        tanActivity.type = tracePassword.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
        tanActivity.typeId = tracePassword.client
          ? tracePassword.client.id
          : tracePassword.clientGroup.id;
        tanActivity.remarks = `"${tracePassword.website}" Credentials Updated by ${user.fullName}`;
        await queryRunner.manager.save(traceActivity);

        // Emit events
        this.eventEmitter.emit(Event_Actions.CREDENTIAL_CREATED, {
          userId,
          clientId: client?.id,
        });

        await queryRunner.commitTransaction();
        return { tanPassword, tracePassword };
      } catch (error) {
        await queryRunner.rollbackTransaction();
        throw error;
      } finally {
        await queryRunner.release();
      }
    };

    let isaddAtomPro = null;

    if (body?.isExistingAtomPro === 'YES') {
      if (body?.websiteUrl === 'https://services.gst.gov.in/services/login') {
        const checkGstrPasswordValid = checkGstrPassword(body.password);
        if (checkGstrPasswordValid) {
          throw new BadRequestException('GSTR Password Is Invalid');
        } else {
          await updatePassword(body, 'YES');
          const gstrCheck = await GstrCredentials.findOne({ where: { passwordId: id } });
          if (gstrCheck) {
            gstrCheck.password = body.password;
            await gstrCheck.save();
          }
        }
      }

      if (
        body.websiteUrl === 'https://eportal.incometax.gov.in/iec/foservices/#/login' &&
        body?.website === 'Income Tax | e-Filing (PAN)'
      ) {
        const incomeTaxCheck = await AutClientCredentials.findOne({ where: { passwordId: id } });
        if (incomeTaxCheck) {
          await updatePassword(body, 'YES');
          incomeTaxCheck.password = body.password;
          await incomeTaxCheck.save();
        }
      }

      if (
        body.websiteUrl === 'https://eportal.incometax.gov.in/iec/foservices/#/login' &&
        body?.website === 'Income Tax | e-Filing (TAN)'
      ) {
        const incomeTaxTanCheck = await TanClientCredentials.findOne({ where: { passwordId: id } });
        if (incomeTaxTanCheck) {
          await updatePassword(body, 'YES');
          incomeTaxTanCheck.password = body.password;
          await incomeTaxTanCheck.save();
        }
      }

      if (
        body.websiteUrl === 'https://www.tdscpc.gov.in/app/login.xhtml?usr=Ded' &&
        body?.website === 'Income Tax | Traces (Tax Deductor)'
      ) {
        const incomeTaxTanCheck = await TanClientCredentials.findOne({
          where: { tanNumber: body?.tracesTan, client: client },
        });
        if (incomeTaxTanCheck) {
          const checkValidTracesUserId = checkTracesUserId(body.loginId.trim());
          const checkValidTracesPassword = checkTracesPassword(body.password.trim());
          if (checkValidTracesUserId) {
            throw new BadRequestException('Trace User Id is Invalid');
          }
          if (checkValidTracesPassword) {
            throw new BadRequestException('Trace Password is Invalid');
          }
          await updateTracePassword(body, 'YES');
          incomeTaxTanCheck.traceUserId = body.loginId;
          incomeTaxTanCheck.tracePassword = body?.password;
          incomeTaxTanCheck.tanNumber = body?.tracesTan;
          await incomeTaxTanCheck.save();
        } else {
          throw new BadRequestException('Income Tax TAN not found for this Trace');
        }
      }
    }

    //If toggle is on
    else if (body?.atomProAdd) {
      if (body?.websiteUrl === 'https://services.gst.gov.in/services/login') {
        const gstrUsername = body.loginId;
        const gstrPassword = body.password;
        const checkGstrUserNameValid = checkGstrUsername(gstrUsername);
        const checkGstrPasswordValid = checkGstrPassword(gstrPassword);
        if (checkGstrUserNameValid) {
          throw new BadRequestException('GSTR User Name Is Invalid');
        } else {
          if (checkGstrPasswordValid) {
            throw new BadRequestException('GSTR Password Is Invalid');
          } else {
            const existingClinet = await GstrCredentials.findOne({
              where: {
                client: client,
              },
            });

            if (existingClinet) {
              throw new BadRequestException('Client already exist in Gstr');
            } else {
              const checkCredential = await GstrCredentials.findOne({
                where: { organizationId: user?.organization?.id, userName: body.loginId },
              });
              if (checkCredential) {
                throw new BadRequestException('Already User Id Existing in this Organization');
              } else {
                const gstrCredentialsCount = await GstrCredentials.count({
                  where: { organizationId: user?.organization?.id, status: GstrStatus.ENABLE },
                });
                const organizationPreferences: any = await OrganizationPreferences.findOne({
                  where: { organization: user?.organization },
                });

                const gstrAccess = organizationPreferences?.automationConfig?.gstr === 'YES';
                const organizationGstrLimit = organizationPreferences?.automationConfig?.gstrLimit;
                if (gstrAccess) {
                  if (organizationGstrLimit > gstrCredentialsCount) {
                    isaddAtomPro = IsExistingAtomPro.YES;
                    await updatePassword(body, isaddAtomPro);
                    const gstrCredentials = new GstrCredentials();
                    gstrCredentials.userName = String(body.loginId);
                    gstrCredentials.password = String(body.password);
                    gstrCredentials.userId = user?.id;
                    gstrCredentials.client = client;
                    gstrCredentials.organizationId = user?.organization?.id;
                    gstrCredentials.status = GstrStatus.ENABLE;
                    gstrCredentials.passwordId = id;
                    await gstrCredentials.save();

                    return 'Atom Pro GSTR Client added Successfully';
                  } else {
                    throw new BadRequestException(
                      'Atom Pro Organization Gstr Client Count Reached',
                    );
                  }
                } else {
                  throw new BadRequestException('Atom Pro Organization No Access');
                }
              }
            }
          }
        }
      } else if (
        body.websiteUrl === 'https://eportal.incometax.gov.in/iec/foservices/#/login' &&
        body?.website === 'Income Tax | e-Filing (PAN)'
      ) {
        const incomeTaxPan = body.loginId;
        const checkPanValid = checkPanNumber(incomeTaxPan);
        if (checkPanValid) {
          throw new BadRequestException('Income Tax Pan Invalid');
        } else {
          const autClientCredential = await AutClientCredentials.count({
            where: { organizationId: user?.organization?.id, status: IncomeTaxStatus.ENABLE },
          });
          const existingClinet = await AutClientCredentials.findOne({
            where: {
              client: client,
            },
          });
          if (existingClinet) {
            throw new BadRequestException('Client already exist in Atom Pro Income Tax');
          } else {
            const existingRecord = await AutClientCredentials.findOne({
              where: { organizationId: user?.organization?.id, panNumber: body.loginId },
            });
            if (existingRecord) {
              throw new BadRequestException(
                'This PAN are already Present in Income Tax in this Organization',
              );
            } else {
              const organizationPreferences: any = await OrganizationPreferences.findOne({
                where: { organization: user?.organization },
              });
              const incomeTaxAccess =
                organizationPreferences?.automationConfig?.incomeTax === 'YES';
              const organizationIncomeTaxLimit =
                organizationPreferences?.automationConfig?.incomeTaxLimit;
              if (incomeTaxAccess) {
                if (organizationIncomeTaxLimit > autClientCredential) {
                  isaddAtomPro = IsExistingAtomPro.YES;
                  await updatePassword(body, isaddAtomPro);
                  const clientCredentials = new AutClientCredentials();
                  clientCredentials.panNumber = String(body.loginId).trim();
                  clientCredentials.password = String(body?.password).trim();
                  clientCredentials.client = client;
                  clientCredentials.organizationId = user?.organization?.id;
                  clientCredentials.syncStatus = syncStatus.NOTSYNC;
                  clientCredentials.status = IncomeTaxStatus.ENABLE;
                  clientCredentials.passwordId = id;
                  await clientCredentials.save();
                  return 'Atom Pro Income Tax Client added Successfully';
                } else {
                  throw new BadRequestException(
                    'Atom Pro Organization Income Tax Client Count Reached',
                  );
                }
              } else {
                throw new BadRequestException('Atom Pro Organization Income Tax No Access');
              }
            }
          }
        }
      } else if (
        body.websiteUrl === 'https://eportal.incometax.gov.in/iec/foservices/#/login' &&
        body?.website === 'Income Tax | e-Filing (TAN)'
      ) {
        const incomeTaxTan = body.loginId;
        const checkTanValid = checkTanNumber(incomeTaxTan);

        if (checkTanValid) {
          throw new BadRequestException('Income Tax TAN is Invalid');
        } else {
          const tanClientCredential = await TanClientCredentials.count({
            where: { organizationId: user?.organization?.id, status: IncomeTaxStatus.ENABLE },
          });
          const existingClinet = await TanClientCredentials.findOne({
            where: {
              client: client,
            },
          });
          if (existingClinet) {
            throw new BadRequestException('Client already exist in Atom Pro Income Tax');
          } else {
            const existingRecord = await TanClientCredentials.findOne({
              where: { organizationId: user?.organization?.id, tanNumber: body.loginId },
            });
            if (existingRecord) {
              throw new BadRequestException(
                'This TAN are already Present in Income Tax in this Organization',
              );
            } else {
              const tanDetails = {
                tanLoginId: body.loginId.trim(),
                tanPassword: body.password.trim(),
                isaddAtomPro: IsExistingAtomPro.YES,
              };
              const tracesDetails = {
                traceTan: body.loginId.trim(),
                tracePassword: null,
                traceUserId: null,
                isaddAtomPro: IsExistingAtomPro.YES,
              };
              const organizationPreferences: any = await OrganizationPreferences.findOne({
                where: { organization: user?.organization },
              });
              const incomeTaxTanAccess = organizationPreferences?.automationConfig?.tan === 'YES';
              const organizationIncomeTaxTanLimit =
                organizationPreferences?.automationConfig?.tanLimit;
              if (incomeTaxTanAccess) {
                if (organizationIncomeTaxTanLimit > tanClientCredential) {
                  try {
                    const tracesPassword = await Password.findOne({
                      where: {
                        client: client,
                        website: 'Income Tax | Traces (Tax Deductor)',
                        tracesTan: String(body.loginId).trim(),
                      },
                    });
                    if (String(body.loginId).trim() !== tracesPassword.tracesTan) {
                      throw new BadRequestException(
                        "TAN doesn't match with Trace TAN. Make sure Income Tax TAN match with Trace TAN",
                      );
                    }
                    const checkValidTracesUserId = checkTracesUserId(tracesPassword.loginId);
                    const checkValidTracesPassword = checkTracesPassword(tracesPassword.password);
                    if (checkValidTracesUserId) {
                      throw new BadRequestException('Trace User Id is Invalid');
                    }
                    if (checkValidTracesPassword) {
                      throw new BadRequestException('Trace Password is Invalid');
                    }
                    // await updateTanAndTracesCredentials(tanDetails,tracesDetails)
                    const { tanPassword, tracePassword } = await executeTanAndTraceTransaction(
                      tanDetails,
                      tracesDetails,
                    );
                    const clientCredentials = new TanClientCredentials();
                    clientCredentials.tanNumber = String(body.loginId).trim();
                    clientCredentials.password = String(body?.password).trim();
                    clientCredentials.client = client;
                    clientCredentials.organizationId = user?.organization?.id;
                    clientCredentials.status = IncomeTaxStatus.ENABLE;
                    clientCredentials.traceUserId = tracesPassword?.loginId || null;
                    clientCredentials.tracePassword = tracesPassword?.password || null;
                    clientCredentials.passwordId = tanPassword?.id;
                    await clientCredentials.save();
                    return 'Atom Pro Income Tax Client added Successfully';
                  } catch (e) {
                    throw new Error(
                      `Error in creating Tan Client Credential for client ${client?.displayName}, error:${e.message}`,
                    );
                  }
                } else {
                  throw new BadRequestException(
                    'Atom Pro Organization Income Tax Client Count Reached',
                  );
                }
              } else {
                throw new BadRequestException('Atom Pro Organization Income Tax No Access');
              }
            }
          }
        }
      } else if (
        body.websiteUrl === 'https://www.tdscpc.gov.in/app/login.xhtml?usr=Ded' &&
        body?.website === 'Income Tax | Traces (Tax Deductor)'
      ) {
        const incomeTaxTan = body.tracesTan;
        const checkTanValid = checkTanNumber(incomeTaxTan);
        if (checkTanValid) {
          throw new BadRequestException('Traces TAN Invalid');
        } else {
          const tanClientCredential = await TanClientCredentials.count({
            where: { organizationId: user?.organization?.id, status: IncomeTaxStatus.ENABLE },
          });
          const existingClinet = await TanClientCredentials.findOne({
            where: {
              client: client,
            },
          });
          if (existingClinet) {
            throw new BadRequestException('Client already exist in Atom Pro Traces');
          } else {
            const existingRecord = await TanClientCredentials.findOne({
              where: { organizationId: user?.organization?.id, traceUserId: body?.tracesTan },
            });
            if (existingRecord) {
              throw new BadRequestException(
                'This TAN are already Present in Traces in this Organization',
              );
            } else {
              const tanDetails = {
                tanLoginId: body.tracesTan.trim(),
                tanPassword: null,
                isaddAtomPro: IsExistingAtomPro.YES,
              };
              const tracesDetails = {
                traceTan: body.tracesTan.trim(),
                tracePassword: body.password.trim(),
                traceUserId: body.loginId.trim(),
                isaddAtomPro: IsExistingAtomPro.YES,
              };
              const organizationPreferences: any = await OrganizationPreferences.findOne({
                where: { organization: user?.organization },
              });
              const incomeTaxTanAccess = organizationPreferences?.automationConfig?.tan === 'YES';
              const organizationIncomeTaxTanLimit =
                organizationPreferences?.automationConfig?.tanLimit;
              if (incomeTaxTanAccess) {
                if (organizationIncomeTaxTanLimit > tanClientCredential) {
                  const tanPasswordRecord = await Password.findOne({
                    where: {
                      client: client,
                      website: 'Income Tax | e-Filing (TAN)',
                      loginId: String(body.tracesTan).trim(),
                    },
                  });
                  if (String(body.tracesTan).trim() !== tanPasswordRecord.loginId) {
                    throw new BadRequestException(
                      "Trace TAN doesn't match with Income Tax TAN. Make sure Income Tax TAN match with Trace TAN",
                    );
                  }
                  const checkValidTracesUserId = checkTracesUserId(body.loginId.trim());
                  const checkValidTracesPassword = checkTracesPassword(body.password.trim());
                  if (checkValidTracesUserId) {
                    throw new BadRequestException('Trace User Id is Invalid');
                  }
                  if (checkValidTracesPassword) {
                    throw new BadRequestException('Trace Password is Invalid');
                  }
                  const { tanPassword, tracePassword } = await executeTanAndTraceTransaction(
                    tanDetails,
                    tracesDetails,
                  );

                  const clientCredentials = new TanClientCredentials();
                  clientCredentials.traceUserId = String(body.loginId).trim();
                  clientCredentials.tracePassword = String(body?.password).trim();
                  clientCredentials.tanNumber = String(tanPassword.loginId).trim();
                  clientCredentials.client = client;
                  clientCredentials.organizationId = user?.organization?.id;
                  clientCredentials.status = IncomeTaxStatus.ENABLE;
                  clientCredentials.passwordId = tanPassword?.id;
                  await clientCredentials.save();
                  return 'Atom Pro Trace Client added Successfully';
                } else {
                  throw new BadRequestException('Atom Pro Organization Trace Client Count Reached');
                }
              } else {
                throw new BadRequestException('Atom Pro Organization Income Tax No Access');
              }
            }
          }
        }
      } else {
        await updatePassword(body, isaddAtomPro);
      }
    }

    //if toggle is off
    else {
      if (body.websiteUrl === 'https://www.tdscpc.gov.in/app/login.xhtml?usr=Ded') {
        await updateTracePassword(body, isaddAtomPro);
      } else {
        await updatePassword(body, isaddAtomPro);
      }
    }

    return password;
  }

  async delete(id: number, userId) {
    let user = await User.findOne({ where: { id: userId } });
    const password = await Password.findOne({
      where: { id },
      relations: ['client', 'clientGroup'],
    });
    password['userId'] = userId;

    if(password?.website === 'Income Tax | e-Filing (TAN)' || password?.website === 'Income Tax | Traces (Tax Deductor)'){
      const counterpartWebsite =
    password.website === 'Income Tax | e-Filing (TAN)'
      ? 'Income Tax | Traces (Tax Deductor)'
      : 'Income Tax | e-Filing (TAN)';
      const otherRecord = await Password.findOne({
        where: {
          client: password.client,
          website: counterpartWebsite,
          tracesTan:(counterpartWebsite === 'Income Tax | Traces (Tax Deductor)' ? password?.loginId  : null)
        },
      });
      if (otherRecord) {
        otherRecord['userId'] = userId;
        await otherRecord.remove();
      }
    }
    await password.remove();

    let activity = new Activity();
    activity.action = Event_Actions.CREDENTIALS_DELETED;
    activity.actorId = user.id;
    activity.type = password.client ? ActivityType.CLIENT : ActivityType.CLIENT_GROUP;
    activity.typeId = password.client ? password.client.id : password.clientGroup.id;
    activity.remarks = `"${password.website}" Credentials Deleted by ${user.fullName}`;
    await activity.save();

    return 'success';
  }

  async getClientCredentials(query,userId){
    const {clientId,type} = query
  if (!clientId) {
    return null;
  }
    const client = await Client.findOne({where:{id:clientId}})
    if(!client){
       return null;
    }

    if(type === 'PAN'){
       const password = await Password.findOne({
      where: { client, website:'Income Tax | e-Filing (PAN)' },
      order: {createdAt:'DESC'}
    });
    return password ?? null;
    }else if(type === 'GST'){
       const password = await Password.findOne({
      where: { client, website:'GST | e-Filing' },
      order: {createdAt:'DESC'}
    });
    return password ?? null;
    } else if(type === 'TAN | TRACES'){
      const tanLatest = await Password.findOne({
        where: { client: clientId, website: 'Income Tax | e-Filing (TAN)' },
        order: { createdAt: 'DESC' },
      });

      const tracesLatest = await Password.findOne({
        where: { client: clientId, website: 'Income Tax | Traces (Tax Deductor)' },
        order: { createdAt: 'DESC' },
      });
        return { tanRecord: tanLatest, traceRecord: tracesLatest }
    }
    return null;
  }
}
