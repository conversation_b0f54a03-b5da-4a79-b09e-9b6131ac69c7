import { applyDecorators } from '@nestjs/common';
import { Type } from 'class-transformer';
import { IsInt, Min, <PERSON>, IsOptional } from 'class-validator';

/**
 * Reusable decorator for `limit` field
 */
export function LimitField(defaultMax = 100) {
  return applyDecorators(
    IsOptional(),
    Type(() => Number),
    IsInt({ message: 'limit must be an integer' }),
    Min(1, { message: 'limit must be at least 1' }),
    Max(defaultMax, { message: `limit cannot exceed ${defaultMax}` }),
  );
}

/**
 * Reusable decorator for `offset` field
 */
export function OffsetField() {
  return applyDecorators(
    IsOptional(),
    Type(() => Number),
    IsInt({ message: 'offset must be an integer' }),
    Min(0, { message: 'offset cannot be negative' }),
  );
}

/**
 * Reusable decorator for `page` field
 */
export function PageField() {
  return applyDecorators(
    IsOptional(),
    Type(() => Number),
    IsInt({ message: 'page must be an integer' }),
    Min(0, { message: 'page cannot be negative' }),
  );
}

/**
 * Base DTO for pagination with offset/limit
 */
export class OffsetPaginationDto {
  @LimitField(100)
  limit?: number = 10;

  @OffsetField()
  offset?: number = 0;
}

/**
 * Base DTO for pagination with page/limit
 */
export class PagePaginationDto {
  @LimitField(100)
  limit?: number;

  @PageField()
  page?: number;
}
