import { Transform, Type } from 'class-transformer';
import { IsOptional, MaxLength, ValidateNested, IsIn } from 'class-validator';
import { BasePaginationDto } from './base-pagination.dto';
import { IsNameOrGstin } from './clientname-gstin-validator';


const SORT_COLUMNS = ['gstIn','demandDt','igstTot','cgstTot','sgstTot','cessTot','totalTot','orderNo'] as const;
type SortColumn = typeof SORT_COLUMNS[number];

class DemandSortDto {
  @IsOptional()
  @IsIn(SORT_COLUMNS as any) column!: SortColumn;
  
  @IsOptional() 
  @IsIn(['asc','desc']) direction!: 'asc' | 'desc';
}

export class GstrDemandsDto extends BasePaginationDto {
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : String(value).trim())
  @MaxLength(100)
  @IsNameOrGstin()
  search?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : String(value))
  fromDate?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : String(value))
  toDate?: string;

  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return undefined;
    try { const obj = typeof value === 'string' ? JSON.parse(value) : value; return Object.assign(new DemandSortDto(), obj); }
    catch { return Object.assign(new DemandSortDto(), { column: '__invalid__', direction: '__invalid__' } as any); }
  })
  @ValidateNested()
  @Type(() => DemandSortDto)
  sort?: DemandSortDto;
}
