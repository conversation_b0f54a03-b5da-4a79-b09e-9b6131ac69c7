import { Transform, Type } from 'class-transformer';
import { IsIn, IsOptional, Matches, MaxLength, ValidateNested } from 'class-validator';
import { IsNameOrGstin } from './clientname-gstin-validator';
import { BasePaginationDto } from './base-pagination.dto';
import { IsYearOrFinYear } from './is-year-or-finyear.validator';
import { CASE_TYPES, CASETYPE_NAMES } from './constants.dto';

const SORT_COLUMNS = ['caseStatus','type','fy','name','createdAt'] as const;
type SortColumn = typeof SORT_COLUMNS[number];

class CaseIdSortDto {
  @IsOptional()
  @IsIn(SORT_COLUMNS as any) column!: SortColumn;

  @IsOptional()
  @IsIn(['asc','desc']) direction!: 'asc' | 'desc';
}
const SAFE_FOLDER_NAME_REGEX = /^[a-zA-Z0-9\s._\/()&,-]+$/;


export class GstrCaseIdsDto extends BasePaginationDto {
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : String(value).trim())
  @MaxLength(100)
  @IsNameOrGstin()
  search?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  // @IsIn(CASETYPE_NAMES as any)
   @Matches(SAFE_FOLDER_NAME_REGEX, {
    message: 'folderType contains invalid or unsafe characters. Only letters, numbers, spaces, and symbols like . _ - / ( ) & , are allowed.',
  })
  type?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(CASE_TYPES as any)
  caseType?: typeof CASE_TYPES[number];

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsYearOrFinYear()
  financialYear?: string;

  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return undefined;
    try { const obj = typeof value === 'string' ? JSON.parse(value) : value; return Object.assign(new CaseIdSortDto(), obj); }
    catch { return Object.assign(new CaseIdSortDto(), { column: '__invalid__', direction: '__invalid__' } as any); }
  })
  @ValidateNested()
  @Type(() => CaseIdSortDto)
  sort?: CaseIdSortDto;
}
