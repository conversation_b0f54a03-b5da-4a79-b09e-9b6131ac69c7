
// tds-analytics

export const formFilingStatus = [
  "Filed",
   "Not Filed",
];

// e-Challan
export const challanSortFilter = [
  "AMOUNT_DESC",
  "AMOUNT_ASC",
  "DATE_NEWEST", 
  "DATE_OLDEST",
];

// My cas
export const udinSortFilter = ["DATE_NEWEST","DATE_OLDEST", "UDIN_COMPLETED", "UDIN_PENDING"];

export const filingType = ["NA", "O", "R"];

// Forms
export  const quarters = ["Q1", "Q2", "Q3", "Q4", "NA"];

export const incomeTaxForms = [
  "Form 5BA",
  "Form 10BBC",
  "Form 10BC",
  "Form 10CCB",
  "Foem 10-IJ",
  "Form 10-IL",
  "Form 15CA",
  "Form 15CB",
  "Form 15CD",
  "Form 15G",
  "Form 15H",
  "Form 24Q",
  "Form 26Q",
  "Form 26A",
  "Form 27BA",
  "Form 27C",
  "Form 27Q",
  "Form 27EQ",
  "Form 34BC",
  "Form 35",
];

export const udinFormStats = [ 'UDIN_APPLICABLE', 'UDIN_NOT_APPLICABLE', 'UDIN_COMPLETED', 'UDIN_PENDING'];

export const tanFormStats = ["Original/Regular", "Revised/Correction"];

// e-Proceedings Excel
export const clientCategories = ["individual", "huf", "partnership_firm","llp", "company", "aop", "boi", "trust", "government", "local_authority", "artificial_judicial_person"];
export const eProFyaCaseStaus = ["Open", "Pending", "Blocked", "Re-enabled"];
export const eProFyiCaseStaus = ["Closed", "Submitted", "Lapsed"];

// Traces Inbox
export const tracesFormTypes = [
  "5BA",
  "10BBC",
  "10BC",
  "10CCB",
  "10-IJ",
  "10-IL",
  "15CA",
  "15CB",
  "15CD",
  "15G",
  "15H",
  "24Q",
  "26Q",
  "26A",
  "27BA",
  "27C",
  "27Q",
  "27EQ",
  "34BC",
  "35",
];

export const tanTracesTypes = ["Required", "Not Required"];

export  const intervalData = [ 'last1week', 'last15days', 'last1month'];

// Trace Outstanding Demand
 export const forms = ["24Q","26Q","27Q","27EQ"];

//REPORTS
export const STATUS = ['PENDING','COMPLETED','INQUEUE'];
export const REMARKS = [
  'Success',
  'Invalid Password, Please retry.',
  'TAN does not exist, Please register this TAN or try with some other TAN.',
  'Login attempt was unsuccessful due to Mult-Factor Authentication',
  'Success with Error',
];

export const tracesRemarks =  ['Success',
    "Invalid User Id, Password or TAN combination",
    "Invalid details",
    "TAN does not exist, Please register this TAN or try with some other TAN.",
    "Login attempt was unsuccessful due to Mult-Factor Authentication",
    "Success with Error",
    "Account locked. After one hour either try with correct password or reset the password via 'Forgot Password' link"
  ]

export const clientNameRegex = /^[^<>:"\/\\|?*\.]*$/;

