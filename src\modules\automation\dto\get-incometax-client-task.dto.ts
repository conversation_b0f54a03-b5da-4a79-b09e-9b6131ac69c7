import { Transform, Type } from 'class-transformer';
import {
  IsInt,
  IsOptional,
  Min,
  Max,
  IsPositive,
} from 'class-validator';
import { OffsetPaginationDto } from './pagination.dto';

export class GetIncometaxClientDto extends OffsetPaginationDto {
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'clientId must be an integer' })
  @Min(1, { message: 'clientId must be greater than 0' })
  clientId?: number;

  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'categoryId must be an integer' })
  @Min(1, { message: 'categoryId must be greater than 0' })
  categoryId?: number;

  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'subCategoryId must be an integer' })
  @Min(1, { message: 'subCategoryId must be greater than 0' })
  subCategoryId?: number;
}
