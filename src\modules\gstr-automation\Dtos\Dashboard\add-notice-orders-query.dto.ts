import { Transform } from 'class-transformer';
import { IsOptional, Matches, IsIn } from 'class-validator';

export class GetAddNoticeOrdersQueryDto {
  @IsOptional()
  @Transform(({ value }) => (value === '' ? undefined : value))
  @Matches(/^\d{4}-\d{4}$/, { 
    message: 'assessmentYear must be in format YYYY-YYYY (e.g., 2025-2026)' 
  })
    assessmentYear?: string;

  @IsOptional()
  @IsIn(['issued_on','response_due_date','manual_due_date','remark_submitted_on'])
  dateField?: 'issued_on'|'response_due_date'|'manual_due_date'|'remark_submitted_on';
}
