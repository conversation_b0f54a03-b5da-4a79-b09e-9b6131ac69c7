import { Transform, Type } from 'class-transformer';
import { IsOptional, <PERSON><PERSON>ength, ValidateNested, IsIn } from 'class-validator';
import { BasePaginationDto } from './base-pagination.dto';
import { IsNameOrGstin } from './clientname-gstin-validator';


const SORT_COLUMNS = ['category','clientId','displayName'] as const;
type SortColumn = typeof SORT_COLUMNS[number];

class ClientSortDto {
  @IsOptional()
  @IsIn(SORT_COLUMNS as any) column!: SortColumn;

  @IsOptional()
  @IsIn(['asc','desc']) direction!: 'asc' | 'desc';
}

export class GstrClientsDto extends BasePaginationDto {
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : String(value).trim())
  @MaxLength(100)
  @IsNameOrGstin({ message: 'search must be a valid client name or a 15-char GSTIN' })
  search?: string;

  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return undefined;
    try { const obj = typeof value === 'string' ? JSON.parse(value) : value; return Object.assign(new ClientSortDto(), obj); }
    catch { return Object.assign(new ClientSortDto(), { column: '__invalid__', direction: '__invalid__' } as any); }
  })
  @ValidateNested()
  @Type(() => ClientSortDto)
  sort?: ClientSortDto;
}
