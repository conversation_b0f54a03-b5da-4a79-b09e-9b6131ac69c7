import { Transform } from 'class-transformer';
import { <PERSON><PERSON>ptional, <PERSON><PERSON>ength, IsIn, IsDateString } from 'class-validator';
import { BasePaginationDto } from './base-pagination.dto';
import { IsNameOrTan } from './clientname-tan-validator';
import { clientCategories, eProFyiCaseStaus } from './constants.dto';
import { IsSafeSqlInput } from './is-safe-sql-input.validator';
import { IsYearOrFinYear } from './is-year-or-finyear.validator';

export class TanFyiNoticeDto extends BasePaginationDto {
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : String(value).trim())
  @MaxLength(100)
  @IsNameOrTan()
  search?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsYearOrFinYear()
  assessmentYear?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(clientCategories as any)
  clientCategory?: typeof clientCategories[number];

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
   @IsSafeSqlInput({ message: 'Invalid section value' })
  section?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(eProFyiCaseStaus as any)
  caseStatus?: typeof eProFyiCaseStaus[number];

   @IsOptional()
  @IsDateString({}, { message: 'fromDate must be a valid ISO8601 datetime string' })
  fromDate?: string;

  @IsOptional()
  @IsDateString({}, { message: 'toDate must be a valid ISO8601 datetime string' })
  toDate?: string;

}
