import { Transform, Type } from 'class-transformer';
import { IsOptional, ValidateNested, IsIn } from 'class-validator';

class UpdatesSortDto {
  @IsOptional()
  @IsIn(['displayName'] as any) column!: 'displayName';

  @IsOptional()
  @IsIn(['asc','desc']) direction!: 'asc' | 'desc';
}

export class GstrUpdatesDto {
  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return undefined;
    try { const obj = typeof value === 'string' ? JSON.parse(value) : value; return Object.assign(new UpdatesSortDto(), obj); }
    catch { return Object.assign(new UpdatesSortDto(), { column: '__invalid__', direction: '__invalid__' } as any); }
  })
  @ValidateNested()
  @Type(() => UpdatesSortDto)
  sort?: UpdatesSortDto;
}
