import {
    IsOptional,
    IsString,
    IsInt,
    Min,
    ValidateNested,
    IsIn,
    IsObject,
    Matches,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { OffsetPaginationDto } from './pagination.dto';
import { IsYearOrFinYear } from './isFInorNot';

class SortDto {
    @IsString()
    @IsIn(['id', 'clientName', 'formDesc', 'ackDt', 'filingTypeCd', 'udinNum'])
    column: string;

    @IsString()
    @IsIn(['ASC', 'DESC'])
    direction: string;
}

export class IncomeTaxFormsQueryDto extends OffsetPaginationDto {
    @IsOptional()
    @IsString()
    @Matches(/^[a-zA-Z0-9\s\-_.]*$/, {
        message:
            'search can only contain alphanumeric characters, spaces, hyphens, underscores, and periods',
    })
    @Transform(({ value }) => value === '' ? undefined : value)
    search?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[a-zA-Z0-9\s\-_.]*$/, {
        message:
            'form Desc can only contain alphanumeric characters, spaces, hyphens, underscores, and periods',
    })
    formDesc?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[a-zA-Z0-9\s\-_.]*$/, {
        message:
            'filing type can only contain alphanumeric characters, spaces, hyphens, underscores, and periods',
    })
    filingType?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsYearOrFinYear()
    assessmentYear?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[a-zA-Z0-9\s\-_.]*$/, {
        message:
            'category can only contain alphanumeric characters, spaces, hyphens, underscores, and periods',
    })
    clientCategory?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    udinStat?: 'UDIN_APPLICABLE' | 'UDIN_NOT_APPLICABLE' | 'UDIN_COMPLETED' | 'UDIN_PENDING';

    @IsOptional()
    @Transform(({ value }) => {
        try {
            return typeof value === 'string' ? JSON.parse(value) : value;
        } catch {
            return {};
        }
    })
    @ValidateNested()
    @Type(() => SortDto)
    sort?: SortDto;
}
