import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Storage, Folder } from '../../schemas/storage.schema';
import { User } from 'src/modules/users/entities/user.entity';
import { StorageSystem } from 'src/modules/gstr-automation/entity/noticeOrders.entity';

@Injectable()
export class MongoStorageService {
    constructor(
        @InjectModel(Storage.name) private storageModel: Model<Storage>,
    ) { }

    async getRootByClient(clientId: string, userId: number): Promise<{ result: Folder[]; breadCrumbs: any[] }> {
        try {
            const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
            if (!user || !user.organization) {
                throw new NotFoundException(`User with ID ${userId} or organization not found`);
            }

            const storage = await this.storageModel
                .findOne({ orgId: user.organization.id, clientId })
                .lean()
                .exec();

            if (!storage) {
                return { result: [], breadCrumbs: [] };
            }

            // Return root children (starts with Atom Pro)
            const result = (storage.root || []).map(({ uid, name, type, storageSystem, fileId, webUrl, createdBy, createdAt, updatedAt, file, fileSize, fileType }) => ({
                uid,
                name,
                type,
                clientId,
                storageSystem,
                fileUrl: file ? `${process.env.AWS_BASE_URL}/${file}` : webUrl,
                fileSize,
                fileType,
                fileId,
                createdBy,
                createdAt,
                updatedAt,
            }));

            // Root call → no breadcrumbs yet (UI will build when user clicks into a folder)
            const breadCrumbs: any[] = [];

            return { result, breadCrumbs };
        } catch (error) {
            throw new NotFoundException(`Failed to retrieve root folders: ${error.message}`);
        }
    };

    async getChildrenByFolderId(clientId: string, userId: number, folderId: string): Promise<{ result: Folder[]; breadCrumbs: any[] }> {
        try {
            const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
            if (!user || !user.organization) {
                throw new NotFoundException(`User with ID ${userId} or organization not found`);
            }

            const storage = await this.storageModel
                .findOne({ orgId: user.organization.id, clientId })
                .lean()
                .exec();

            if (!storage) {
                return { result: [], breadCrumbs: [] };
            }

            // Recursive function to find folder & build breadcrumbs
            function findFolderWithPath(
                folders: Folder[],
                uid: string,
                path: any[] = [],
            ): { folder: Folder | null; path: any[] } {
                for (const folder of folders) {
                    const currentPath = [...path, { name: folder.name, uid: folder.uid }];

                    if (folder.uid === uid && folder.type === 'folder') {
                        return { folder, path: currentPath };
                    }

                    if (folder.children && folder.children.length) {
                        const found = findFolderWithPath(folder.children, uid, currentPath);
                        if (found.folder) return found;
                    }
                }
                return { folder: null, path: [] };
            }

            const { folder, path } = findFolderWithPath(storage.root, folderId);

            if (!folder) {
                return { result: [], breadCrumbs: [] };
            }

            const result = (folder.children || []).map(({ uid, name, type, file, fileSize, fileType, storageSystem, fileId, webUrl, createdBy, createdAt, updatedAt }) => ({
                uid,
                name,
                type,
                clientId,
                storageSystem,
                fileUrl: (file && storageSystem === StorageSystem.AMAZON) ? `${process.env.AWS_BASE_URL}/${file}` : webUrl,
                fileSize,
                fileType,
                fileId,
                webUrl,
                createdBy,
                createdAt,
                updatedAt,
            }));

            // Breadcrumbs start from "Atom Pro" (no clientId folder to drop)
            return { result, breadCrumbs: path };
        } catch (error) {
            throw new NotFoundException(`Failed to retrieve folder children: ${error.message}`);
        }
    };

    async getTotalFileSize(userId: number): Promise<number> {
        const user = await User.findOne({ where: { id: userId }, relations: ['organization'] });
        if (!user || !user.organization) {
            throw new NotFoundException(`User with ID ${userId} or organization not found`);
        }

        try {
            const storages = await this.storageModel
                .find({ orgId: user.organization.id })
                .lean()
                .exec();

            if (!storages || storages.length === 0) {
                return 0;
            }

            function calculateTotalSize(folders: Folder[]): number {
                let total = 0;
                for (const item of folders) {
                    if (item.type === 'file') {
                        total += item.fileSize || 0;
                    } else if (item.type === 'folder' && item.children) {
                        total += calculateTotalSize(item.children);
                    }
                }
                return total;
            }

            const totalSize = storages.reduce((sum, storage) => {
                return sum + calculateTotalSize(storage.root || []);
            }, 0);
            return totalSize;
        } catch (error) {
            throw new NotFoundException(`Failed to calculate total file size: ${error.message}`);
        }
    }
}