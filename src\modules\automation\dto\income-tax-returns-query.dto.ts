import {
    IsOptional,
    IsString,
    IsInt,
    Min,
    ValidateNested,
    IsIn,
    Matches,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { FILING_TYPES_VALUES, ITR_TYPES_VALUES, VERIFICATION_STATUS_VALUES } from './constants.dto';
import { OffsetPaginationDto } from './pagination.dto';
import { BadRequestException } from '@nestjs/common';
import { IsYearOrFinYear } from './isFInorNot';

class SortDto {
    @IsString()
    @IsIn([
        'assmentYear',
        'category',
        'clientId',
        'filingTypeCd',
        'id',
        'verStatus',
        'formtypeCd',
        'timeLineDate',
        'timeLineDesc',
    ])
    column: string;

    @IsString()
    @IsIn(['ASC', 'DESC'])
    direction: string;
}

export class IncomeTaxReturnsQueryDto extends OffsetPaginationDto {
    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[a-zA-Z0-9\s\-_.]*$/, {
        message:
            'search can only contain alphanumeric characters, spaces, hyphens, underscores, and periods',
    })
    search?: string;

    @IsOptional()
    @Transform(({ value }) => {
        try {
            if (typeof value === 'string') {
                return JSON.parse(value);
            }
            return value;
        } catch {
            throw new BadRequestException('Invalid sort format');
        }
    })
    @ValidateNested()
    @Type(() => SortDto)
    sort?: SortDto;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsIn(ITR_TYPES_VALUES)
    itrType?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsIn(FILING_TYPES_VALUES)
    filingType?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsYearOrFinYear()
    assessmentYear?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[a-zA-Z0-9\s\-_.]*$/, {
        message:
            'category can only contain alphanumeric characters, spaces, hyphens, underscores, and periods',
    })
    clientCategory?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsIn(VERIFICATION_STATUS_VALUES)
    status?: string;
}
