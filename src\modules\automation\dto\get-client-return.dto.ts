import { BadRequestException } from "@nestjs/common";
import { Transform, Type } from "class-transformer";
import { IsInt, IsOptional, Min, Max, ValidateNested, IsIn, IsObject } from "class-validator";
import { OffsetPaginationDto } from "./pagination.dto";

export class SortDto {
  @IsOptional()
  @IsIn(
    ["assmentYear", "filingTypeCd", "id", "verStatus", "formtypeCd", "timeLineDate", "timeLineDesc"],
    { message: "Invalid sort column" }
  )
  column?: string;

  @IsOptional()
  @IsIn(["ASC", "DESC", "asc", "desc"], { message: "Sort direction must be ASC or DESC" })
  direction?: string = "DESC";
}

export class GetClientReturnQueryDto extends OffsetPaginationDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => SortDto)
  @Transform(({ value }) => {
    if (!value) return undefined;
    try {
      return typeof value === "string" ? JSON.parse(value) : value;
    } catch {
      throw new BadRequestException("Invalid sort parameter: must be valid JSON");
    }
  })
  sort?: SortDto;
}
