import { Transform, Type } from 'class-transformer';
import {
  IsInt,
  IsOptional,
  Min,
  Max,
  IsIn,
  ValidateNested,
  IsObject,
} from 'class-validator';
import { OffsetPaginationDto } from './pagination.dto';
import { BadRequestException } from '@nestjs/common';

class SortDto {
  @IsIn(
    [
      'id',
      'caName',
      'caMembershipNum',
      'filingType',
      'formTypeCd',
      'transactionNo',
      'assignedDate',
      'udinNumber',
    ],
    { message: 'Invalid sort column' },
  )
  column: string;

  @IsIn(['ASC', 'DESC', 'asc', 'desc'], {
    message: 'Sort direction must be ASC or DESC',
  })
  direction: string;
}

export class GetClientMycasDto extends OffsetPaginationDto {
  @IsOptional()
  @Transform(({ value }) => {
    // if (!value) return undefined;

    // If it's a string, try to parse
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        throw new BadRequestException('Invalid sort format');
      }
    }

    if (typeof value !== 'object') {
      throw new BadRequestException('Invalid sort format');
    }

    return value;
  })
  @ValidateNested()
  @Type(() => SortDto)
  sort?: SortDto;
}
