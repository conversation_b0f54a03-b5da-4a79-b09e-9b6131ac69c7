import { Type, Transform } from 'class-transformer';
import {
    IsOptional,
    IsString,
    <PERSON>Int,
    <PERSON>,
    <PERSON>,
    IsIn,
    Matches,
    MaxLength,
} from 'class-validator';
import { OffsetPaginationDto } from './pagination.dto';
import { IsYearOrFinYear } from './isFInorNot';

export const ALLOWED_TYPES = ['Self', 'Other'];
export const MAX_SEARCH_LENGTH = 100;

// DTO for e-Proceedings query
export class IncomeTaxEproceedingsQueryDto extends OffsetPaginationDto {
    @IsOptional()
    @IsString()
    @Matches(/^[A-Za-z0-9_\- ]+$/, {
        message: 'search contains invalid characters',
    })
    @Transform(({ value }) => value === '' ? undefined : value)
    @MaxLength(MAX_SEARCH_LENGTH)
    search?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[A-Za-z0-9_\-() ]+$/, {
        message: 'section contains invalid characters',
    })
    section?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsYearOrFinYear()
    assessmentYear?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[A-Za-z0-9_\- ]+$/, {
        message: 'Category contains invalid characters',
    })
    clientCategory?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsIn(ALLOWED_TYPES)
    type?: string;

    @IsOptional()
    @Transform(({ value }) => {
        if (typeof value === 'string') {
            try {
                return JSON.parse(value);
            } catch {
                return {};
            }
        }
        return value || {};
    })
    sort?: { column?: string; direction?: 'ASC' | 'DESC' };
}
