import { Transform, Type } from 'class-transformer';
import {
    IsInt,
    IsOptional,
    IsString,
    IsIn,
    Min,
    Max,
    ValidateIf,
    Matches,
    ValidateNested,
} from 'class-validator';
import { OffsetPaginationDto } from './pagination.dto';

export class SortDto {
  @IsString()
  @IsIn(['id', 'pan', 'status', 'remarks', 'createdAt'])
  column: string;

  @IsString()
  @IsIn(['asc', 'desc'])
  direction: 'asc' | 'desc';
}

export class IncomeTaxReportQueryDto extends OffsetPaginationDto {
    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsIn(['PENDING', 'COMPLETED', 'INQUEUE'])
    status?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^Success$|^Invalid Password, Please retry\.$|^PAN does not exist, Please register this PAN or try with some other PAN\.$|^Login attempt was unsuccessful due to Mult-Factor Authentication$|^Success with Error$/)
    remarks?: string;

    @IsOptional()
    @ValidateNested()
    @Transform(({ value }) => {
        try {
            return typeof value === 'string' ? JSON.parse(value) : value;
        } catch {
            return {};
        }
    })
    @Type(() => SortDto)
    sort?: string; 
}
