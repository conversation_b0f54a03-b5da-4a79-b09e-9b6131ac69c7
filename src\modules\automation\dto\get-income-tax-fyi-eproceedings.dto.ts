import { Transform, Type } from 'class-transformer';
import {
    IsOptional,
    IsString,
    <PERSON>Int,
    Min,
    Max,
    IsIn,
    IsNumberString,
    Length,
    Matches,
    MaxLength,
} from 'class-validator';
import { OffsetPaginationDto } from './pagination.dto';
import { IsYearOrFinYear } from './isFInorNot';

export class GetIncomeTaxFyiEproceedingsDto extends OffsetPaginationDto {
    @IsOptional()
    @IsString()
    @MaxLength(100)
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[A-Za-z0-9_\- ]+$/, {
        message: 'search contains invalid characters',
    })
    search?: string;

    @IsOptional()
    @IsString()
    @MaxLength(50)
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[a-zA-Z0-9 ()_-]*$/, { message: 'section must contain only letters, numbers, spaces, underscores, or dashes' })
    section?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsYearOrFinYear()
    assessmentYear?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @MaxLength(50)
    @Matches(/^[A-Za-z0-9_\- ]+$/, {
        message: 'Category contains invalid characters',
    })
    clientCategory?: string;

    @IsOptional()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsIn(['Self', 'Other'])
    type?: string;
}
