import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ _id: false })
export class Folder {
    @Prop({ required: true })
    uid: string;

    @Prop()
    name?: string; // Made optional to match Node.js schema

    @Prop({ required: true, enum: ['folder', 'file'] })
    type: 'folder' | 'file';

    @Prop()
    file?: string;

    @Prop()
    fileSize?: number;

    @Prop()
    fileType?: string;

    @Prop()
    storageSystem?: string;

    @Prop()
    fileId?: string;

    @Prop()
    webUrl?: string;

    @Prop()
    createdBy?: string;

    @Prop()
    createdAt?: Date;

    @Prop()
    updatedAt?: Date;

    @Prop({ type: Object, default: {} })
    metadata?: any;

    @Prop({ type: [Object], default: [] })
    children?: Folder[];
}

export const FolderSchema = SchemaFactory.createForClass(Folder);

// Explicitly define recursive children
FolderSchema.add({ children: { type: [FolderSchema], default: [] } });

@Schema({ collection: 'storages' })
export class Storage extends Document {
    @Prop({ required: true })
    orgId: string;

    @Prop({ required: true })
    clientId: string;

    @Prop({ type: [FolderSchema], default: [] })
    root: Folder[];

    @Prop()
    atomFileId?: string;

    @Prop()
    clientsFileId?: string;

    @Prop()
    displayNameFileId?: string;

    @Prop({ type: Number })
    __v?: number;
}

export const StorageSchema = SchemaFactory.createForClass(Storage);