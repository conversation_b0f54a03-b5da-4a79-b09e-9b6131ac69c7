import { Transform, Type } from 'class-transformer';
import { IsIn, IsOptional, Matches, MaxLength, ValidateNested } from 'class-validator';
import { BasePaginationDto } from './base-pagination.dto';
import { IsNameOrGstin } from './clientname-gstin-validator';
import { IsYearOrFinYear } from './is-year-or-finyear.validator';
import {
  CASE_TYPES,
  CASETYPE_NAMES,
  FOLDER_TYPES,
  INTERVALS,
  removeReminderType,
  RESPONSE_TYPES,
  uniqueType,
} from './constants.dto';

const SORT_COLUMNS = [
  'name',
  'fy',
  'casetype',
  'folder',
  'type',
  'categoryDate',
  'dueDate',
  'createdType',
  'description'
] as const;
type SortColumn = (typeof SORT_COLUMNS)[number];


class GstrAddSortDto {
  @IsOptional()
  @IsIn(SORT_COLUMNS as any)
  column!: SortColumn;

  @IsOptional()
  @IsIn(['asc', 'desc'])
  direction!: 'asc' | 'desc';
}


const SAFE_FOLDER_NAME_REGEX = /^[a-zA-Z0-9\s._\/()&,-]+$/;

export class GstrAdditionalNoticeOrdersDto extends BasePaginationDto {
  @IsOptional()
  @Transform(({ value }) => (value === '' ? undefined : String(value).trim()))
  @MaxLength(100)
  @IsNameOrGstin({ message: 'search must be a valid client name or a 15-char GSTIN' })
  search?: string;

  @IsOptional()
  @Transform(({ value }) => (value === '' ? undefined : value))
    @Matches(SAFE_FOLDER_NAME_REGEX, {
  message: 'folderType contains invalid or unsafe characters. Only letters, numbers, spaces, and symbols like . _ - / ( ) & , are allowed.',
})
  type?: string; 

  @IsOptional()
  @Transform(({ value }) => (value === '' ? undefined : value))
  @IsYearOrFinYear({ message: 'financialYear must be YYYY or YYYY-YYYY or NA' })
  financialYear?: string; 

  @IsOptional()
  @Transform(({ value }) => (value === '' ? undefined : value))
  @IsIn(INTERVALS as any)
  interval?: (typeof INTERVALS)[number];

  @IsOptional()
  @Transform(({ value }) => (value === '' ? undefined : value))
  @Matches(SAFE_FOLDER_NAME_REGEX, {
  message: 'folderType contains invalid or unsafe characters. Only letters, numbers, spaces, and symbols like . _ - / ( ) & , are allowed.',
})
  folderType?: string;

  @IsOptional()
  @Transform(({ value }) => (value === '' ? undefined : value))
  @IsIn(RESPONSE_TYPES as any)
  responseType?: (typeof RESPONSE_TYPES)[number];

  @IsOptional()
  @Transform(({ value }) => (value === '' ? undefined : value))
  @IsIn(CASE_TYPES as any)
  caseType?: (typeof CASE_TYPES)[number];

  // Accept sort as JSON ("sort={...}") or bracket syntax ("sort[column]=...&sort[direction]=...")
  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return undefined;
    try {
      const obj = typeof value === 'string' ? JSON.parse(value) : value;
      return Object.assign(new GstrAddSortDto(), obj);
    } catch {
      // Force a validation error by returning invalid values
      return Object.assign(new GstrAddSortDto(), {
        column: '__invalid__',
        direction: '__invalid__',
      } as any);
    }
  })
  @ValidateNested()
  @Type(() => GstrAddSortDto)
  sort?: GstrAddSortDto;

  @IsOptional()
  dueInterval: string;

  @IsOptional()
  @Transform(({ value }) => (value === '' ? undefined : value))
  @IsIn(uniqueType as any)
  uniqueType?: (typeof uniqueType)[number];

  @IsOptional()
   @Transform(({ value }) => (value === '' ? undefined : value))
  @IsIn(removeReminderType as any)
  removeReminder?: (typeof removeReminderType)[number];
}
