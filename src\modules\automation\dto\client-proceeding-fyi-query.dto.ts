import { IsInt, IsOptional, IsString, Min, IsPositive, IsIn, Matches, MaxLength } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { OffsetPaginationDto } from './pagination.dto';
import { IsYearOrFinYear } from './isFInorNot';

export class ClientProceedingFyiQueryDto extends OffsetPaginationDto {
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value === '' ? undefined : value)
  @Matches(/^[a-zA-Z0-9 _-]*$/, { message: 'section must contain only letters, numbers, spaces, underscores, or dashes' })
  section?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsYearOrFinYear()
  assessmentYear?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => value === '' ? undefined : value)
  @Matches(/^[a-zA-Z0-9 _-]*$/, { message: 'type must contain only letters, numbers, spaces, underscores, or dashes' })
  type?: string;

  @IsOptional()
  @IsString()
  @Transform(({ value }) => value === '' ? undefined : value)
  @MaxLength(100)
  @Matches(/^[a-zA-Z0-9 _-]*$/, { message: 'search query must be alphanumeric' })
  search?: string;
}
