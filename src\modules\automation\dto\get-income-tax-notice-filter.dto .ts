import { Type, Transform } from 'class-transformer';
import {
    IsOptional,
    IsString,
    Matches,
    MaxLength,
    IsDateString,
} from 'class-validator';
import { OffsetPaginationDto } from './pagination.dto';
import { IsYearOrFinYear } from './isFInorNot';

export class GetIncomeTaxNoticeFilterDto extends OffsetPaginationDto {
    @IsOptional()
    @IsString()
    @MaxLength(100)
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[A-Za-z0-9_\- ]+$/, {
        message: 'search contains invalid characters',
    })
    search?: string;

    @IsOptional()
    @IsDateString({}, { message: 'fromDate must be a valid ISO 8601 date string' })
    fromDate?: string;

    @IsOptional()
    @IsDateString({}, { message: 'toDate must be a valid ISO 8601 date string' })
    toDate?: string;

    @IsOptional()
    @IsString()
    @MaxLength(50)
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsYearOrFinYear()
    assessmentYear?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @MaxLength(50)
    @Matches(/^[A-Za-z0-9_\- ]+$/, {
        message: 'Category contains invalid characters',
    })
    clientCategory?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @MaxLength(50)
    @Matches(/^[A-Za-z0-9_\- ]+$/, {
        message: 'Interval  contains invalid characters',
    })
    interval?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @MaxLength(50)
    @Matches(/^[A-Za-z0-9_\- ]+$/, {
        message: 'Sort By  contains invalid characters',
    })
    sortBy?: string;


}
