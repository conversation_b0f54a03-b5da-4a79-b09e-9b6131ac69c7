import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';
import { clientNameRegex, gstInRegex } from './constants.dto';

export function IsNameOrGstin(validationOptions?: ValidationOptions) {
  const nameRegex = clientNameRegex;
  const gstinRegex = gstInRegex;
  return (target: Object, propertyName: string) => {
    registerDecorator({
      name: 'IsNameOrGstin',
      target: target.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {
          if (value == null || value === '') return true;
          if (typeof value !== 'string') return false;
          const v = value.trim();
          const maybe = v.replace(/\s+/g, '').toUpperCase();
          if (maybe.length === 15 && /^[0-9A-Z]+$/.test(maybe)) return gstinRegex.test(maybe);
          return nameRegex.test(v);
        },
        defaultMessage(args?: ValidationArguments) {
          return `${args?.property} must be a valid client name or a 15-char GSTIN`;
        },
      },
    });
  };
}
