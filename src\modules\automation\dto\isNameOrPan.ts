import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';
import { BadRequestException } from '@nestjs/common';

export function IsNameOrPan(
  options?: {
    nameRegex?: RegExp;
    panRegex?: RegExp;
    trim?: boolean;
    uppercasePan?: boolean;
  },
  validationOptions?: ValidationOptions,
) {
  const {
    nameRegex = /^[a-zA-Z0-9\s\.\-&]+$/, // stricter name
    panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]$/, // PAN format
    trim = true,
    uppercasePan = true,
  } = options || {};

  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'IsNameOrPan',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          if (value === undefined || value === null || value === '') return true;
          if (typeof value !== 'string') {
            throw new BadRequestException(`${args.property} must be a string`);
          }

          let v = value;
          if (trim) v = v.trim();

          // ❌ reject SQL injection characters/keywords
          const forbidden = /(\bselect\b|\binsert\b|\bupdate\b|\bdelete\b|\bdrop\b|\bunion\b|\bsleep\b|\*|\(|\)|\+|;)/i;
          if (forbidden.test(v)) {
            throw new BadRequestException(
              `${args.property} contains forbidden characters/keywords`,
            );
          }

          const maybePan = v.replace(/\s+/g, '');
          const panToTest = uppercasePan ? maybePan.toUpperCase() : maybePan;

          // ✅ PAN check
          if (panToTest.length === 10 && /^[A-Z0-9]+$/.test(panToTest)) {
            if (panRegex.test(panToTest)) return true;
            throw new BadRequestException(`${args.property} must be a valid PAN (**********).`);
          }

          // ✅ Name check
          if (!nameRegex.test(v)) {
            throw new BadRequestException(
              `${args.property} must be a valid Client Name (letters/numbers/spaces only).`,
            );
          }

          return true;
        },
      },
    });
  };
}
