import { Transform, Type } from 'class-transformer';
import { IsIn, IsOptional, <PERSON><PERSON>eng<PERSON>, ValidateNested } from 'class-validator';
import { BasePaginationDto } from './base-pagination.dto';
import { IsNameOrGstin } from './clientname-gstin-validator';
import { TYPES } from './constants.dto';


const SORT_COLUMNS = ['gstIn','igst','cgst','sgst','cess','total'] as const;
type SortColumn = typeof SORT_COLUMNS[number];

class LedgerSortDto {
  @IsOptional()
  @IsIn(SORT_COLUMNS as any) column!: SortColumn;

  @IsOptional()
  @IsIn(['asc','desc']) direction!: 'asc' | 'desc';
}

export class GstrLedgersDto extends BasePaginationDto {
  @IsOptional()
  @Transform(({ value }) => (value === '' ? undefined : String(value).trim()))
  @MaxLength(100)
  @IsNameOrGstin({ message: 'search must be a valid client name or a 15-char GSTIN' })
  search?: string;
  
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(TYPES as any)
  type?: typeof TYPES[number];

  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return undefined;
    try { const obj = typeof value === 'string' ? JSON.parse(value) : value; return Object.assign(new LedgerSortDto(), obj); }
    catch { return Object.assign(new LedgerSortDto(), { column: '__invalid__', direction: '__invalid__' } as any); }
  })
  @ValidateNested()
  @Type(() => LedgerSortDto)
  sort?: LedgerSortDto;
}
