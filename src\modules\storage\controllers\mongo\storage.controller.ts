import { Controller, Get, Query, BadRequestException, UseGuards, Req } from '@nestjs/common';
;
import { Folder } from '../../schemas/storage.schema';
import { MongoStorageService } from '../../services/mongo-services/storage.service';
import { JwtAuthGuard } from 'src/modules/users/jwt/jwt-auth.guard';


@UseGuards(JwtAuthGuard)
@Controller('mongo-storage')
export class MongoStorageController {
    constructor(private readonly storageService: MongoStorageService) { }
    @Get()
    async getStorage(
        @Req() req: any,
        @Query('clientId') clientId: string,
        @Query('folderId') folderId?: string,

    ) {
        if (!clientId) {
            throw new BadRequestException('clientId is required');
        } const { userId } = req.user;
        let data

        if (folderId) {
            // fetch children if folderId is given
            data = await this.storageService.getChildrenByFolderId(clientId, userId, folderId);
        } else {
            // fetch root if folderId is missing
            data = await this.storageService.getRootByClient(clientId, userId);
        }

        return data;
    };


    @Get('organization-storage')
    async getOrganizationStorage(@Req() req: any) {
        const { userId } = req.user;
        const totalSize = await this.storageService.getTotalFileSize(userId);
        return { totalSize };
    };

}
