import { IsOptional, IsS<PERSON>, <PERSON>Int, Min, IsIn, IsAlphanumeric, Matches } from 'class-validator';
import { Transform } from 'class-transformer';
import { OffsetPaginationDto } from './pagination.dto';
import { IsYearOrFinYear } from './isFInorNot';

export class IncomeTaxEchallansQueryDto extends OffsetPaginationDto {
    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[A-Za-z0-9_\- ]+$/, {
        message: 'search contains invalid characters',
    })
    search?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsYearOrFinYear()
    assessmentYear?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[A-Za-z0-9_\- ]+$/, {
        message: 'category contains invalid characters',
    })
    clientCategory?: string;

    @IsOptional()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsString()
    sortValue?: 'AMOUNT_DESC' | 'AMOUNT_ASC' | 'DATE_NEWEST' | 'DATE_OLDEST';

    @IsOptional()
    @Transform(({ value }) => {
        try {
            return typeof value === 'string' && value.trim() !== '' ? JSON.parse(value) : null;
        } catch {
            return null;
        }
    })
    sort?: {
        column: 'id' | 'name' | 'minorDesc' | 'paymentTime' | 'acin' | 'totalAmt';
        direction: 'ASC' | 'DESC';
    };
}
