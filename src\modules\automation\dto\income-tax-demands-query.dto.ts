import { Type, Transform } from 'class-transformer';
import {
    IsOptional,
    IsString,
    <PERSON>Int,
    <PERSON>,
    Max,
    Matches,
    IsIn,
    <PERSON><PERSON>teIf,
    <PERSON><PERSON>ength,
} from 'class-validator';
import { OffsetPaginationDto } from './pagination.dto';
import { IsYearOrFinYear } from './isFInorNot';

export class IncomeTaxDemandsQueryDto extends OffsetPaginationDto {
    @IsOptional()
    @IsString()
    @Matches(/^[a-zA-Z0-9\s\-_.]*$/, {
        message:
            'search can only contain alphanumeric characters, spaces, hyphens, underscores, and periods',
    })
    @Transform(({ value }) => value === '' ? undefined : value)
    @MaxLength(100)
    search?: string;

    @IsOptional()
    @IsString()
    @Matches(/^[a-zA-Z0-9\-]*$/, {
        message: 'section must contain only letters, numbers, or dashes',
    })
    @Transform(({ value }) => value === '' ? undefined : value)
    section?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsYearOrFinYear()
    assessmentYear?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[A-Za-z0-9_\- ]+$/, {
        message: 'category contains invalid characters',
    })
    clientCategory?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsIn(['last1week', 'last15days', 'last1month'])
    interval?: string;

    @IsOptional()
    @Transform(({ value }) => {
        if (!value || typeof value !== 'string') return null;
        try {
            const parsed = JSON.parse(value);
            if (
                parsed.column &&
                ['assessmentYear', 'category', 'name', 'sectionCodeText', 'demandRaisedDate', 'currentStatus', 'outstandingDemandAmount'].includes(parsed.column)
            ) {
                parsed.direction = parsed.direction?.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';
                return parsed;
            }
            return null;
        } catch {
            return null;
        }
    })
    sort?: {
        column: 'assessmentYear' | 'category' | 'name' | 'sectionCodeText' | 'demandRaisedDate' | 'currentStatus' | 'outstandingDemandAmount';
        direction: 'ASC' | 'DESC';
    };

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsIn(['AMOUNT_DESC', 'AMOUNT_ASC', 'DATE_NEWEST', 'DATE_OLDEST'])
    sortValue?: string;
}
