import { Transform, Type } from 'class-transformer';
import { IsIn, IsOptional, ValidateNested } from 'class-validator';
import { BasePaginationDto } from './base-pagination.dto';
import { REMARKS, STATUS } from './constants.dto';

const SORT_COLUMNS = ['displayName','tanNumber','status','remarks','updatedAt','clientId'] as const;
type SortColumn = typeof SORT_COLUMNS[number];

class ReportSortDto {
  @IsOptional()
  @IsIn(SORT_COLUMNS as any) column!: SortColumn;
    
  @IsOptional()
  @IsIn(['asc','desc']) direction!: 'asc' | 'desc';
}



export class TanReportDto extends BasePaginationDto {
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(STATUS as any)
  status?: typeof STATUS[number];

  // If remarks list changes often, consider making this a free string.
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(REMARKS as any)
  remarks?: typeof REMARKS[number];

  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === '') return undefined;
    if (typeof value === 'string') {
      try {
        const obj = JSON.parse(value);
        return Object.assign(new ReportSortDto(), obj); // hydrate class instance
      } catch {
        // Force a validation error by returning invalid values
        return Object.assign(new ReportSortDto(), { column: '__invalid__', direction: '__invalid__' });
      }
    }
    // If client sends object via bracket syntax, also hydrate
    return Object.assign(new ReportSortDto(), value);
  })
  @ValidateNested()
  @Type(() => ReportSortDto)
  sort?: ReportSortDto;
}
