import { Transform, Type } from 'class-transformer';
import { IsOptional, <PERSON><PERSON>ength, ValidateNested, IsIn, IsDateString } from 'class-validator';
import { BasePaginationDto } from './base-pagination.dto';
import { IsNameOrTan } from './clientname-tan-validator';
import { challanSortFilter, clientCategories, eProFyaCaseStaus, incomeTaxForms, quarters, tanFormStats, udinFormStats } from './constants.dto';
import { IsSafeSqlInput } from './is-safe-sql-input.validator';
import { IsYearOrFinYear } from './is-year-or-finyear.validator';

export class TanFyaNoticeDto extends BasePaginationDto {
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : String(value).trim())
  @MaxLength(100)
  @IsNameOrTan()
  search?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsYearOrFinYear()
  assessmentYear?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(clientCategories as any)
  clientCategory?: typeof clientCategories[number];

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
   @IsSafeSqlInput({ message: 'Invalid section value' })
  section?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(eProFyaCaseStaus as any)
  caseStatus?: typeof eProFyaCaseStaus[number];

  @IsOptional()
  @IsDateString({}, { message: 'fromDate must be a valid ISO8601 datetime string' })
  fromDate?: string;

  @IsOptional()
  @IsDateString({}, { message: 'toDate must be a valid ISO8601 datetime string' })
  toDate?: string;
}
