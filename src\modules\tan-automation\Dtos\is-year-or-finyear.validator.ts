import { registerDecorator, ValidationArguments, ValidationOptions } from 'class-validator';

export function IsYearOrFinYear(opts?: ValidationOptions) {
  const reYear = /^\d{4}$/;
  const reFin = /^\d{4}-(\d{2}|\d{4})$/; // supports 2025-26 or 2025-2026
  return (target: Object, propertyName: string) => {
    registerDecorator({
      name: 'IsYearOrFinYear',
      target: target.constructor,
      propertyName,
      options: opts,
      validator: {
        validate(value: any) {
          if (value == null || value === '') return true;
          if (value === 'NA') return true;  
          if (typeof value !== 'string') return false;
          return reYear.test(value) || reFin.test(value);
        },
        defaultMessage(_: ValidationArguments) {
          return `must be 'YYYY' or 'YYYY-YYYY' (e.g., 2025 or 2025-2026) or 'NA'`;
        },
      },
    });
  };
}
