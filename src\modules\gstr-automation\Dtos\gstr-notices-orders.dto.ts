import { Transform } from 'class-transformer';
import { IsOptional, Matches, IsIn } from 'class-validator';

export class GstrNoticeOrdersDto {
  // Allowed format e.g. 2023-24; adjust if yours is different
  @IsOptional()
 @Transform(({ value }) => value === '' ? undefined : value)  // empty string => undefined
  @Matches(/^\d{4}$/, { message: 'assessmentYear must be like YYYY (e.g., 2025)' })  assessmentYear?: string;

  // If you let clients choose a date field, whitelist it
  @IsOptional()
  @IsIn(['issued_on','response_due_date','manual_due_date','remark_submitted_on'])
  dateField?: 'issued_on'|'response_due_date'|'manual_due_date'|'remark_submitted_on';
}
