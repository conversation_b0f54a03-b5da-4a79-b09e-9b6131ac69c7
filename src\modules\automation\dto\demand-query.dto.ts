// demand-query.dto.ts
import { Type, Transform } from 'class-transformer';
import {
  IsInt,
  Min,
  Max,
  IsOptional,
  ValidateNested,
  IsIn,
  IsObject,
} from 'class-validator';
import { OffsetPaginationDto } from './pagination.dto';
import { BadGatewayException, BadRequestException } from '@nestjs/common';

class SortDto {
  @IsIn([
    'assessmentYear',
    'demandRaisedDate',
    'sectionCodeText',
    'outstandingDemandAmount',
  ])
  column: string;

  @IsIn(['ASC', 'DESC', 'asc', 'desc'])
  direction: string;
}

export class DemandQueryDto extends OffsetPaginationDto {
  @IsOptional()
  @Transform(({ value }) => {
    try {
      if (typeof value === 'string') {
        return JSON.parse(value);
      }
      return value;
    } catch {
      throw new BadRequestException('Invalid sort format');
    }
  })
  @ValidateNested()
  @IsObject({ message: 'sort must be a valid object' })
  @Type(() => SortDto)
  sort?: SortDto;
}
