import { Transform } from 'class-transformer';
import { IsOptional } from 'class-validator';
import { BasePaginationDto } from '../base-pagination.dto';
import { IsYearOrFinYear } from '../is-year-or-finyear.validator';


export class TanEproceedingsDto extends BasePaginationDto {
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsYearOrFinYear()
  financialYear?: string;

}
