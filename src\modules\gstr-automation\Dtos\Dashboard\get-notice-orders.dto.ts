import { Transform } from 'class-transformer';
import {
  IsIn,
  IsInt,
  IsOptional,
  IsString,
  Min,
  <PERSON><PERSON><PERSON>th,
  <PERSON><PERSON><PERSON><PERSON>ested,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { IsNameOrGstin } from '../clientname-gstin-validator';

class SortDto {
  @IsOptional()
  @IsIn([
    'name',
    'issuedBy',
    'type',
    'dateOfIssuance',
    'dueDate',
    'amountOfDemand', // <-- added to match your example
  ])
  column!: 'name' | 'issuedBy' | 'type' | 'dateOfIssuance' | 'dueDate' | 'amountOfDemand';

  @IsOptional()
  @IsIn(['asc', 'desc'])
  direction!: 'asc' | 'desc';
}

export class GetNoticeAndOrdersDto {
  @IsOptional()
  @Transform(({ value }) => (value === '' ? undefined : String(value).trim()))
  @MaxLength(100)
  @IsNameOrGstin({ message: 'search must be a valid client name or a 15-char GSTIN' })
  search?: string;

  @IsOptional()
  @IsIn(['System Generated', 'Telangana', ''])
  issuedBy?: string;

  @IsOptional()
  @IsIn(['Notice', 'Order', ''])
  type?: 'Notice' | 'Order' | '';

  @IsOptional()
  @IsIn(['today', 'last1week', 'last15days', 'last1month', ''])
  interval?: 'today' | 'last1week' | 'last15days' | 'last1month' | '';

  @IsOptional()
  @IsIn(['today', 'next1week', 'next15days', 'next1month', ''])
  dueInterval?: 'today' | 'next1week' | 'next15days' | 'next1month' | '';

  @IsOptional()
  @Transform(({ value }) => Number(value))
  @IsInt()
  @Min(0)
  offset: number = 0;

  @IsOptional()
  @Transform(({ value }) => Number(value))
  @IsInt()
  @Min(1)
  @Max(100) // cap to avoid abuse
  limit: number = 10;

  @IsOptional()
  @Transform(({ value }) => {
    if (value === undefined || value === '') return undefined;
    if (typeof value === 'string') {
      try {
        const obj = JSON.parse(value);
        return Object.assign(new SortDto(), obj); // hydrate class instance
      } catch {
        // Force a validation error by returning invalid values
        return Object.assign(new SortDto(), { column: '__invalid__', direction: '__invalid__' });
      }
    }
    // If client sends object via bracket syntax, also hydrate
    return Object.assign(new SortDto(), value);
  })
  @ValidateNested()
  @Type(() => SortDto)
  sort?: SortDto;
}
