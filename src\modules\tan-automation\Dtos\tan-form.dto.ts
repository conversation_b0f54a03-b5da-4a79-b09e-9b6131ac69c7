import { Transform, Type } from 'class-transformer';
import { IsOptional, MaxLength, ValidateNested, IsIn } from 'class-validator';
import { BasePaginationDto } from './base-pagination.dto';
import { IsNameOrTan } from './clientname-tan-validator';
import { challanSortFilter, incomeTaxForms, quarters, tanFormStats, udinFormStats } from './constants.dto';
import { IsYearOrFinYear } from './is-year-or-finyear.validator';


const SORT_COLUMNS = ['id','financialQuarter','forms','formDesc','ackDt','tempAckNo','filingTypeCd'] as const;
type SortColumn = typeof SORT_COLUMNS[number];

class SortDto {
  @IsOptional()
  @IsIn(SORT_COLUMNS as any) column!: SortColumn;
  
  @IsOptional() 
  @IsIn(['asc','desc']) direction!: 'asc' | 'desc';
}

export class TanFormDto extends BasePaginationDto {
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : String(value).trim())
  @MaxLength(100)
  @IsNameOrTan()
  search?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsYearOrFinYear()
  financialYear?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(incomeTaxForms as any)
  formCode?: typeof incomeTaxForms[number];

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(tanFormStats as any)
  filingType?: typeof tanFormStats[number];

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(udinFormStats as any)
  udinStat?: typeof udinFormStats[number];

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(quarters as any)
  financialQuarter?: typeof quarters[number];

  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return undefined;
    try { const obj = typeof value === 'string' ? JSON.parse(value) : value; return Object.assign(new SortDto(), obj); }
    catch { return Object.assign(new SortDto(), { column: '__invalid__', direction: '__invalid__' } as any); }
  })
  @ValidateNested()
  @Type(() => SortDto)
  sort?: SortDto;
}
