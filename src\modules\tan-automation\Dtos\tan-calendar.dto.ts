// tan-calendar.dto.ts
import { Transform } from 'class-transformer';
import { IsOptional, Matches } from 'class-validator';

export class TanCalendarDto {
  /**
   * Normalizes:
   *  - ?startDates=2024-02-01
   *  - ?startDates=2024-02-01&startDates=2024-02-10
   *  - ?startDates=2024-02-01,2024-02-10
   *
   * Returns single string 'YYYY-MM-DD' (first value) or undefined.
   */
  @Transform(({ value }) => {
    if (value == null || value === '') return undefined;
    // if array, take first
    if (Array.isArray(value)) {
      const v = String(value[0] ?? '').trim();
      return v === '' ? undefined : v.split(',')[0].trim();
    }
    // single string or csv
    const s = String(value).trim();
    if (s === '') return undefined;
    return s.includes(',') ? s.split(',')[0].trim() : s;
  })
  @IsOptional()
  // strict YYYY-MM-DD
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'startDates must be in YYYY-MM-DD format',
  })
  startDates?: string;
}
