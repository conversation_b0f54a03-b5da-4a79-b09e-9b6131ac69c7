import {
    IsOptional,
    IsString,
    IsInt,
    Min,
    ValidateNested,
    IsIn,
    Matches,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { OffsetPaginationDto } from './pagination.dto';
import { IsYearOrFinYear } from './isFInorNot';

class SortDto {
    @IsString()
    @IsIn([
        'id',
        'name',
        'filingType',
        'formTypeCd',
        'transactionNo',
        'assignedDate',
        'udinNumber',
    ])
    column: string;

    @IsString()
    @IsIn(['ASC', 'DESC'])
    direction: string;
}

export class IncomeTaxMyCasQueryDto extends OffsetPaginationDto {
    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[a-zA-Z0-9\s\-_.]*$/, {
        message:
            'search can only contain alphanumeric characters, spaces, hyphens, underscores, and periods',
    })
    search?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsYearOrFinYear()
    assessmentYear?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[a-zA-Z0-9\s\-_.]*$/, {
        message:
            'category can only contain alphanumeric characters, spaces, hyphens, underscores, and periods',
    })
    clientCategory?: string;

    @IsOptional()
    @Transform(({ value }) => {
        try {
            return typeof value === 'string' ? JSON.parse(value) : value;
        } catch {
            return {};
        }
    })
    @ValidateNested()
    @Type(() => SortDto)
    sort?: SortDto;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsIn(['DATE_NEWEST', 'DATE_OLDEST', 'UDIN_COMPLETED', 'UDIN_PENDING'])
    sortValue?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsIn(['NA', 'O', 'R']) // Filing Type Values from frontend select
    filingValue?: string;
}
