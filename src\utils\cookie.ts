import { Response } from 'express';
import { encryptData } from './crypto';

export function setAuthCookie(
  res: Response,
  token: string,
  cookieName = 'jwt',
  maxAgeMs: number = 30 * 60 * 1000, // default 15m
) {
  const origin = (res.req.headers.origin || '').toLowerCase();
  const host = (res.req.headers.host || '').toLowerCase();

  // 1. Is the frontend running on localhost or a local IP?
  const isLocalFrontend =
    origin.startsWith('http://localhost') ||
    origin.startsWith('http://127.0.0.1') ||
    origin.startsWith('http://192.168.') ||
    origin.includes('.local') ||
    host.includes('localhost') ||
    host.includes('127.0.0.1');
  console.log({ origin, host, isLocalFrontend });
  // 2. Are we deployed to staging/production?
  const isProdLike = ['production', 'staging'].includes(process.env.NODE_ENV || '');

  // Critical rules:
  // - When frontend is localhost → NO domain + secure=false + SameSite=lax
  // - When frontend is on vider.in domain → domain=.vider.in + secure=true + SameSite=none

  const useSecureCookie = isProdLike && !isLocalFrontend;
  console.log({ useSecureCookie }, 'node env:', process.env.NODE_ENV);
  res.cookie(cookieName, encryptData(token), {
    httpOnly: true,
    secure: true, // false for localhost
    sameSite: useSecureCookie ? 'none' : 'none', // lax is fine for localhost
    maxAge: maxAgeMs,
    path: '/',
    // Only set domain when it's safe and useful
  domain: useSecureCookie ? '.vider.in' : undefined,
  });
}

// res.cookie(cookieName, encryptData(token), {
//   httpOnly: true,
//   secure: process.env.NODE_ENV === 'production',
//   sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
//   maxAge: maxAgeMs,
//   domain: process.env.NODE_ENV === 'production' ? '.vider.in' : 'localhost',
// });
