import { Transform } from 'class-transformer';
import { IsOptional, MaxLength, IsIn } from 'class-validator';
import { BasePaginationDto } from './base-pagination.dto';
import { IsNameOrTan } from './clientname-tan-validator';
import {  formFilingStatus, incomeTaxForms, quarters } from './constants.dto';
import { IsYearOrFinYear } from './is-year-or-finyear.validator';

export class TanTdsAnalyticsDto extends BasePaginationDto {
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : String(value).trim())
  @MaxLength(100)
  @IsNameOrTan()
  search?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsYearOrFinYear()
  financialYear?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(incomeTaxForms as any)
  formCd?: typeof incomeTaxForms[number];

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(formFilingStatus as any)
  filingStatus?: typeof formFilingStatus[number];

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(quarters as any)
  financialQuarter?: typeof quarters[number];

}
