import { Type, Transform } from 'class-transformer';
import {
    IsOptional,
    IsString,
    IsInt,
    Min,
    MaxLength,
    IsDateString,
    ValidateNested,
    Matches,
} from 'class-validator';
import { OffsetPaginationDto } from './pagination.dto';
import { IsYearOrFinYear } from './isFInorNot';

export class GetIncomeTaxTempNoticeFyiDto extends OffsetPaginationDto {
    @IsOptional()
    @IsString()
    @MaxLength(100)
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[A-Za-z0-9_\- ]+$/, {
        message: 'search contains invalid characters',
    })
    search?: string;

    @IsOptional()
    @IsString()
    @MaxLength(50)
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[a-zA-Z0-9 ()_-]*$/, { message: 'section must contain only letters, numbers, spaces, underscores, or dashes' })
    section?: string;

    @IsOptional()
    @IsString()
    @MaxLength(50)
    @Transform(({ value }) => value === '' ? undefined : value)
    @IsYearOrFinYear()
    assessmentYear?: string;

    @IsOptional()
    @IsString()
    @Transform(({ value }) => value === '' ? undefined : value)
    @MaxLength(50)
    @Matches(/^[A-Za-z0-9_\- ]+$/, {
        message: 'Category contains invalid characters',
    })
    clientCategory?: string;

    @IsOptional()
    @IsString()
    @MaxLength(50)
    @Transform(({ value }) => value === '' ? undefined : value)
    @Matches(/^[A-Za-z0-9_\- ]+$/, {
        message: 'Case Status contains invalid characters',
    })
    caseStatus?: string;

    @IsOptional()
    @IsDateString({}, { message: 'fromDate must be a valid ISO 8601 date string' })
    fromDate?: string;

    @IsOptional()
    @IsDateString({}, { message: 'toDate must be a valid ISO 8601 date string' })
    toDate?: string;

    @IsOptional()
    @Transform(({ value }) => {
        try {
            return typeof value === 'string' && value.trim() !== '' ? JSON.parse(value) : null;
        } catch {
            return null;
        }
    })
    sort?: {
        column: 'assesmentYear' | 'type' | 'category' | 'displayName' | 'noticeSentDate' |
        'proceedingName' | 'proceedingStatus' | 'dateOfCompliance' |
        'dateResponseSubmitted' | 'noticeSection' | 'proceedingLimitationDate';
        direction: 'ASC' | 'DESC';
    };
}
