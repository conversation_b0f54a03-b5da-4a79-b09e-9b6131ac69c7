import { Transform, Type } from 'class-transformer';
import { IsInt, IsOptional, IsString, Min, MaxLength } from 'class-validator';
import { IsNameOrPan } from './isNameOrPan';
import { PagePaginationDto } from './pagination.dto';

export class GetClientsDto extends PagePaginationDto {
  @IsOptional()
  @IsString()
  @MaxLength(100)
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsNameOrPan({}, { message: 'Search term must be a valid name or PAN' })
  search?: string;
}
