import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  registerDecorator,
  ValidationOptions
} from 'class-validator';

const MAX_LEN = 100; // tune as needed
const DISALLOWED_PATTERNS = [
  /--/, /;|\/\*/, /\*\/|'/, /"/, /`/, /\b(SELECT|INSERT|UPDATE|DELETE|DROP|ALTER|TRUNCATE|EXEC|MERGE|UNION|CREATE)\b/i
];

const ALLOWED_CHARS = /^[A-Za-z0-9 _\-\.\:\/\(\)]+$/;

@ValidatorConstraint({ async: false })
export class IsSafeSqlInputConstraint implements ValidatorConstraintInterface {
  validate(value: any, _args: ValidationArguments) {
    if (value == null || value === '') return true; // leave required checks to @IsNotEmpty if desired
    if (typeof value !== 'string') return false;
    if (value.length > MAX_LEN) return false;

    // allow characters: letters, numbers, space, underscore, dash, dot
    if (!ALLOWED_CHARS.test(value)) return false;


for (const p of DISALLOWED_PATTERNS) {
      if (typeof p === 'string') {
        if (value.toLowerCase().includes(p)) return false;
      } else {
        if (p.test(value)) return false;
      }
    }

    return true;
  }

  defaultMessage(_args: ValidationArguments) {
    return 'section contains invalid or potentially unsafe characters';
  }
}

export function IsSafeSqlInput(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsSafeSqlInputConstraint,
    });
  };
}
