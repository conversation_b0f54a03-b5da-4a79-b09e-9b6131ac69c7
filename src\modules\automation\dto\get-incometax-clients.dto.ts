import { Transform, Type } from 'class-transformer';
import { IsInt, IsOptional, IsString, Min, ValidateNested, IsIn } from 'class-validator';
import { IsNameOrPan } from './isNameOrPan';
import { BadRequestException } from '@nestjs/common';
import { OffsetPaginationDto } from './pagination.dto';

class SortDto {
  @IsString()
  @IsIn(['client', 'name', 'Category']) // restrict allowed sortable columns
  column: string;

  @IsString()
  @IsIn(['ASC', 'DESC', 'asc', 'desc']) // restrict allowed directions
  direction: string;
}

export class GetClientAutCredentialsDto extends OffsetPaginationDto {
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsNameOrPan({}, { message: 'Search term must be a valid name or PAN' })
  search?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => SortDto)
  @Transform(({ value }) => {
    if (!value) return undefined;
    try {
      return JSON.parse(value);
    } catch {
      throw new BadRequestException('Invalid sort parameter: must be valid JSON');
    }
  })
  sort?: SortDto;
}
