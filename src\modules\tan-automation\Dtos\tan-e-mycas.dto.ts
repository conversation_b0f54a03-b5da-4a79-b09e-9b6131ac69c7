import { Transform, Type } from 'class-transformer';
import { IsOptional, <PERSON><PERSON>ength, ValidateNested, IsIn, Matches } from 'class-validator';
import { BasePaginationDto } from './base-pagination.dto';
import { IsNameOrTan } from './clientname-tan-validator';
import { challanSortFilter, filingType } from './constants.dto';
import { IsYearOrFinYear } from './is-year-or-finyear.validator';


const SORT_COLUMNS = ['id','name','minorDesc','natureOfPayment','paymentTime','acin','totalAmt'] as const;
type SortColumn = typeof SORT_COLUMNS[number];

class SortDto {
  @IsOptional()
  @IsIn(SORT_COLUMNS as any) column!: SortColumn;
  
  @IsOptional() 
  @IsIn(['asc','desc']) direction!: 'asc' | 'desc';
}

export class TanMycasDto extends BasePaginationDto {
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : String(value).trim())
  @MaxLength(100)
  @IsNameOrTan()
  search?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsYearOrFinYear()
  assessmentYear?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(challanSortFilter as any)
  sortValue?: typeof challanSortFilter[number];

   @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @Matches(/^[A-Za-z0-9.-]+$/, {
    message: 'Only letters, numbers, "-" and "." are allowed',
  })
  formTypeCd?: string;

  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : value)
  @IsIn(filingType as any)
  filingValue?: typeof filingType[number];

  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return undefined;
    try { const obj = typeof value === 'string' ? JSON.parse(value) : value; return Object.assign(new SortDto(), obj); }
    catch { return Object.assign(new SortDto(), { column: '__invalid__', direction: '__invalid__' } as any); }
  })
  @ValidateNested()
  @Type(() => SortDto)
  sort?: SortDto;
}
