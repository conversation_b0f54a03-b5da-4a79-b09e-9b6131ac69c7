import { Transform, Type } from 'class-transformer';
import { IsOptional, Max<PERSON>ength, ValidateNested, IsIn } from 'class-validator';
import { BasePaginationDto } from './base-pagination.dto';
import { IsNameOrTan } from './clientname-tan-validator';


const SORT_COLUMNS = ['category','clientId','displayName','tanNumber'] as const;
type SortColumn = typeof SORT_COLUMNS[number];

class ClientSortDto {
  @IsOptional()
  @IsIn(SORT_COLUMNS as any) column!: SortColumn;

  @IsOptional()
  @IsIn(['asc','desc']) direction!: 'asc' | 'desc';
}

export class TanClientsDto extends BasePaginationDto {
  @IsOptional()
  @Transform(({ value }) => value === '' ? undefined : String(value).trim())
  @MaxLength(100)
  @IsNameOrTan({ message: 'search must be a valid client name or a 10-char TAN' })
  search?: string;

  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return undefined;
    try { const obj = typeof value === 'string' ? JSON.parse(value) : value; return Object.assign(new ClientSortDto(), obj); }
    catch { return Object.assign(new ClientSortDto(), { column: '__invalid__', direction: '__invalid__' } as any); }
  })
  @ValidateNested()
  @Type(() => ClientSortDto)
  sort?: ClientSortDto;
}
